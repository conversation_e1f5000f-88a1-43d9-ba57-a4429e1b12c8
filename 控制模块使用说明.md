# 智能小车控制模块使用说明

## 📋 概述

本控制模块从STM32智能小车项目中提取了所有核心控制逻辑，重新封装为独立的C语言模块。模块包含PID控制、电机驱动、循迹算法、姿态控制等功能，可以直接移植到其他项目中使用。

## 📁 文件结构

```
控制模块/
├── robot_control.h              # 控制模块头文件
├── robot_control.c              # 控制模块实现文件
├── robot_control_example.c      # 使用示例代码
└── 控制模块使用说明.md          # 本说明文档
```

## 🚀 快速开始

### 1. 文件集成

将以下文件添加到你的项目中：
- `robot_control.h` - 头文件
- `robot_control.c` - 实现文件

在你的主程序中包含头文件：
```c
#include "robot_control.h"
```

### 2. 基础初始化

```c
int main(void)
{
    // 1. 初始化控制系统
    RobotControl_Init();
    
    // 2. 注册硬件回调函数 (必须实现)
    RobotControl_RegisterMotorCallback(Your_Motor_Callback);
    RobotControl_RegisterSensorCallback(Your_Sensor_Callback);
    RobotControl_RegisterAttitudeCallback(Your_Attitude_Callback);
    
    // 3. 开始控制循环
    while(1) {
        RobotControl_Update();
        // 你的控制逻辑
        HAL_Delay(5);  // 5ms控制周期
    }
}
```

### 3. 实现硬件回调函数

你需要根据实际硬件实现以下三个回调函数：

#### 电机PWM回调函数
```c
void Your_Motor_Callback(uint8_t motor_id, int16_t pwm, bool direction)
{
    if (motor_id == 0) {  // 左电机
        // 设置左电机方向和PWM
        if (direction) {
            HAL_GPIO_WritePin(MOTOR_1_GPIO_Port, MOTOR_1_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(MOTOR_2_GPIO_Port, MOTOR_2_Pin, GPIO_PIN_RESET);
        } else {
            HAL_GPIO_WritePin(MOTOR_1_GPIO_Port, MOTOR_1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(MOTOR_2_GPIO_Port, MOTOR_2_Pin, GPIO_PIN_SET);
        }
        __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_1, pwm);
    }
    else if (motor_id == 1) {  // 右电机
        // 设置右电机方向和PWM
        if (direction) {
            HAL_GPIO_WritePin(MOTOR_3_GPIO_Port, MOTOR_3_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(MOTOR_4_GPIO_Port, MOTOR_4_Pin, GPIO_PIN_SET);
        } else {
            HAL_GPIO_WritePin(MOTOR_3_GPIO_Port, MOTOR_3_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(MOTOR_4_GPIO_Port, MOTOR_4_Pin, GPIO_PIN_RESET);
        }
        __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_2, pwm);
    }
}
```

#### 传感器读取回调函数
```c
void Your_Sensor_Callback(uint8_t sensor_id, uint8_t *value)
{
    switch (sensor_id) {
        case 0: *value = HAL_GPIO_ReadPin(G1_GPIO_Port, G1_Pin); break;
        case 1: *value = HAL_GPIO_ReadPin(G2_GPIO_Port, G2_Pin); break;
        case 2: *value = HAL_GPIO_ReadPin(G3_GPIO_Port, G3_Pin); break;
        case 3: *value = HAL_GPIO_ReadPin(G4_GPIO_Port, G4_Pin); break;
        case 4: *value = HAL_GPIO_ReadPin(G5_GPIO_Port, G5_Pin); break;
        case 5: *value = HAL_GPIO_ReadPin(G6_GPIO_Port, G6_Pin); break;
        case 6: *value = HAL_GPIO_ReadPin(G7_GPIO_Port, G7_Pin); break;
        default: *value = 0; break;
    }
}
```

#### 姿态读取回调函数
```c
void Your_Attitude_Callback(float *yaw)
{
    // 从你的IMU传感器读取偏航角
    // 这里需要根据你的IMU通信协议实现
    *yaw = Get_IMU_Yaw();  // 你的IMU读取函数
}
```

## 🎮 功能模块使用

### 1. PID控制器

#### 创建和初始化PID控制器
```c
PID_Controller_t pid_motor;
PID_Init(&pid_motor, 28.34f, 4.54f, 0.0f);  // Kp, Ki, Kd
```

#### 设置输出限制
```c
PID_SetLimits(&pid_motor, -2000.0f, 2000.0f);
```

#### PID计算
```c
float current_speed = Get_Motor_Speed();  // 获取当前速度
float target_speed = 100.0f;             // 目标速度
float pwm_output = PID_Calculate(&pid_motor, current_speed, target_speed);
```

#### 重置PID控制器
```c
PID_Reset(&pid_motor);  // 清除历史数据
```

### 2. 循迹控制

#### 读取循迹传感器
```c
LineTracker_Data_t line_data;
LineTracker_ReadSensors(&line_data);

// 检查结果
if (line_data.line_detected) {
    printf("检测到线，位置: %d\n", line_data.line_position);
}
```

#### 计算循迹速度
```c
int16_t left_speed, right_speed;
LineTracker_GetMotorSpeeds(&line_data, &left_speed, &right_speed);

// 应用到电机
Motor_SetPWM(&motor_left, left_speed);
Motor_SetPWM(&motor_right, right_speed);
```

### 3. 姿态控制

#### 初始化姿态数据
```c
Attitude_Data_t attitude;
Attitude_Init(&attitude);
```

#### 更新姿态数据
```c
float current_yaw = Get_IMU_Yaw();
Attitude_UpdateYaw(&attitude, current_yaw);
```

#### 设置目标角度
```c
Attitude_SetTarget(&attitude, 90.0f);  // 目标转向90度
```

#### 姿态控制计算
```c
int16_t left_speed, right_speed;
Motion_AttitudeControl(&attitude, 20, &left_speed, &right_speed);
```

### 4. 电机控制

#### 设置电机PWM
```c
Motor_Control_t motor_left, motor_right;

Motor_SetPWM(&motor_left, 1500);   // 正值前进
Motor_SetPWM(&motor_right, -800);  // 负值后退
```

#### 停止电机
```c
Motor_Stop(&motor_left);
Motor_Emergency_Stop();  // 紧急停止所有电机
```

## 🔧 高级使用

### 1. 完整控制循环示例

```c
void Control_Loop(void)
{
    // 数据结构
    LineTracker_Data_t line_data;
    Attitude_Data_t attitude;
    PID_Controller_t pid_left, pid_right;
    
    // 初始化
    Attitude_Init(&attitude);
    PID_Init(&pid_left, 28.34f, 4.54f, 0.0f);
    PID_Init(&pid_right, 28.34f, 4.54f, -0.66f);
    
    while(1) {
        // 1. 读取传感器
        LineTracker_ReadSensors(&line_data);
        
        // 2. 更新姿态
        float current_yaw;
        Your_Attitude_Callback(&current_yaw);
        Attitude_UpdateYaw(&attitude, current_yaw);
        
        // 3. 决策控制模式
        int16_t target_left, target_right;
        
        if (line_data.line_detected) {
            // 循迹模式
            LineTracker_GetMotorSpeeds(&line_data, &target_left, &target_right);
        } else {
            // 姿态控制模式
            Motion_AttitudeControl(&attitude, 20, &target_left, &target_right);
        }
        
        // 4. PID控制
        float current_left = Get_Left_Motor_Speed();
        float current_right = Get_Right_Motor_Speed();
        
        float pwm_left = PID_Calculate(&pid_left, current_left, target_left);
        float pwm_right = PID_Calculate(&pid_right, current_right, target_right);
        
        // 5. 输出到电机
        Your_Motor_Callback(0, (int16_t)pwm_left, pwm_left >= 0);
        Your_Motor_Callback(1, (int16_t)pwm_right, pwm_right >= 0);
        
        // 6. 控制周期延时
        HAL_Delay(5);  // 5ms控制周期
    }
}
```

### 2. 自定义PID参数

```c
// 根据你的电机特性调整PID参数
PID_Controller_t custom_pid;
PID_Init(&custom_pid, 
    25.0f,  // Kp - 比例系数，影响响应速度
    3.0f,   // Ki - 积分系数，消除稳态误差
    0.5f    // Kd - 微分系数，抑制振荡
);
```

### 3. 多模式控制

```c
typedef enum {
    MODE_IDLE = 0,
    MODE_LINE_FOLLOW = 1,
    MODE_ATTITUDE_CONTROL = 2,
    MODE_MANUAL = 3
} Control_Mode_t;

void Multi_Mode_Control(Control_Mode_t mode)
{
    switch(mode) {
        case MODE_LINE_FOLLOW:
            // 循迹控制逻辑
            break;
        case MODE_ATTITUDE_CONTROL:
            // 姿态控制逻辑
            break;
        case MODE_MANUAL:
            // 手动控制逻辑
            break;
        default:
            Motor_Emergency_Stop();
            break;
    }
}
```

## ⚙️ 配置参数

### 1. PID参数调优指南

| 参数 | 作用 | 调优建议 |
|------|------|----------|
| Kp | 比例系数，影响响应速度 | 从小开始增加，过大会振荡 |
| Ki | 积分系数，消除稳态误差 | 通常为Kp的1/5到1/10 |
| Kd | 微分系数，抑制振荡 | 可以为0，或者很小的正值 |

### 2. 控制周期设置

```c
#define CONTROL_PERIOD_MS  5    // 推荐5ms控制周期
```

### 3. 传感器配置

```c
#define LINE_SENSOR_COUNT  7    // 循迹传感器数量
```

## 🐛 常见问题

### 1. 编译错误

**问题**: 找不到头文件
**解决**: 确保`robot_control.h`在包含路径中

**问题**: 链接错误
**解决**: 确保`robot_control.c`已添加到项目编译列表

### 2. 运行问题

**问题**: 电机不转
**解决**: 检查电机回调函数是否正确实现和注册

**问题**: PID控制不稳定
**解决**: 调整PID参数，特别是Kp值

**问题**: 循迹效果差
**解决**: 检查传感器回调函数和传感器硬件连接

### 3. 性能优化

- 控制周期保持在5ms
- 避免在回调函数中执行耗时操作
- 合理设置PID输出限制

## 📚 参考资料

- 查看`robot_control_example.c`获取完整使用示例
- 参考原项目文档了解算法原理
- STM32 HAL库文档

## 🤝 技术支持

如有问题，请：
1. 检查使用示例代码
2. 确认硬件连接正确
3. 验证回调函数实现
4. 调整控制参数

---

**💡 提示**: 建议先运行示例代码熟悉接口，再根据实际需求进行定制开发。
