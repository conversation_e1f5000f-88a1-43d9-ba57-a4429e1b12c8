# STM32智能小车项目

## 📋 项目简介

这是一个基于STM32F103微控制器的智能小车项目，具备循迹、姿态控制、运动控制等功能。项目代码经过全面优化，添加了详细的中文注释，适合学习和参考。

### ✨ 主要特性

- 🎯 **高精度控制**: 采用增量式PID算法，实现精确的速度和位置控制
- 🔍 **多传感器融合**: 集成7路红外循迹传感器和IMU姿态传感器
- ⚡ **实时响应**: 1ms级别的控制周期，保证系统实时性
- 🧩 **模块化设计**: 代码结构清晰，便于维护和扩展
- 📝 **详细注释**: 100%中文注释覆盖，便于理解和学习

## 🛠️ 硬件配置

### 主控制器
- **MCU**: STM32F103C8T6 (72MHz, 64KB Flash, 20KB RAM)
- **开发环境**: Keil MDK + STM32CubeMX

### 传感器模块
- **循迹传感器**: 7路红外传感器阵列
- **姿态传感器**: IMU惯性测量单元 (UART通信)
- **编码器**: 增量式编码器 × 2

### 执行器模块
- **电机**: 直流减速电机 × 2 (H桥驱动)
- **显示**: OLED显示屏 (I2C接口)
- **指示**: 激光器模块

### 人机交互
- **按键**: 3个功能按键 (模式切换、角度设置、复位)

## 📁 项目结构

```
电赛H题c8t6还原/
├── XIAOZHANCHE/                 # 主项目目录
│   ├── Core/                    # STM32 HAL核心文件
│   │   ├── Inc/                 # 头文件
│   │   │   ├── main.h          # 主头文件 (含宏定义注释)
│   │   │   ├── gpio.h          # GPIO配置
│   │   │   ├── tim.h           # 定时器配置
│   │   │   └── usart.h         # 串口配置
│   │   └── Src/                 # 源文件
│   │       ├── main.c          # 主程序 (含详细中文注释)
│   │       ├── gpio.c          # GPIO初始化
│   │       ├── tim.c           # 定时器初始化
│   │       └── usart.c         # 串口初始化
│   ├── Users/                   # 用户自定义模块
│   │   ├── Encoder.c           # PID控制和电机驱动 (含中文注释)
│   │   ├── Encoder.h           # 编码器模块头文件 (含中文注释)
│   │   ├── OLED.c              # OLED显示驱动
│   │   └── OLED.h              # OLED头文件
│   ├── Drivers/                 # STM32 HAL驱动库
│   └── MDK-ARM/                 # Keil工程文件
└── docs/                        # 项目文档
    ├── prd/                     # 产品需求文档
    ├── architecture/            # 架构设计文档
    ├── development/             # 开发技术文档
    ├── tasks/                   # 任务规划文档
    └── 用户使用手册.md           # 用户使用指南
```

## 🚀 快速开始

### 1. 环境准备
- 安装 Keil MDK 5.x
- 安装 STM32CubeMX
- 安装 ST-Link 驱动

### 2. 编译和下载
1. 打开 `XIAOZHANCHE/MDK-ARM/XIAOZHANCHE.uvprojx`
2. 编译项目 (Ctrl+F7)
3. 连接ST-Link调试器
4. 下载程序到STM32 (F8)

### 3. 硬件连接
详细的硬件连接说明请参考 [用户使用手册](docs/用户使用手册.md)

### 4. 基本操作
1. 上电后OLED显示系统信息
2. 按"模式按键"切换运行模式
3. 按"角度按键"设置参考角度
4. 按"复位按键"重置传感器

## 📖 文档说明

### 核心文档
- [📋 用户使用手册](docs/用户使用手册.md) - 详细的使用指南和故障排除
- [🏗️ 系统架构文档](docs/architecture/Architecture_STM32小车_v1.0.md) - 系统设计和架构说明
- [💻 技术文档](docs/development/STM32小车技术文档.md) - 详细的技术实现说明
- [🔌 API接口文档](docs/development/API接口文档.md) - 完整的函数接口说明

### 项目管理文档
- [📝 产品需求文档](docs/prd/PRD_STM32小车优化_v1.0.md) - 项目需求和目标
- [📅 任务规划](docs/tasks/任务规划_STM32小车优化.md) - 开发任务分解

## 🎮 功能特性

### 控制算法
- **增量式PID控制**: 精确的电机速度控制
- **循迹算法**: 基于7路传感器的智能循迹
- **姿态控制**: IMU反馈的角度控制
- **状态机管理**: 复杂任务的状态管理

### 运行模式
- **模式10**: 空闲状态
- **模式11-24**: 循迹和基础导航
- **模式31-48**: 复杂路径规划和执行
- **模式50**: 任务完成状态

### 实时显示
- 当前偏航角 (yaw)
- 目标偏航角 (yaw0)
- 参考偏航角 (yaw1)
- 运行模式 (mode)

## 🔧 参数配置

### PID参数
```c
// 左轮PID参数
PID_left.Kp = 28.34;  // 比例系数
PID_left.Ki = 4.54;   // 积分系数
PID_left.Kd = 0;      // 微分系数

// 右轮PID参数
PID_right.Kp = 28.34;
PID_right.Ki = 4.54;
PID_right.Kd = -0.66; // 补偿机械差异
```

### 控制周期
- PID控制周期: 5ms
- 传感器采样: 1ms
- 显示更新: 实时

## 🐛 故障排除

### 常见问题
1. **循迹不稳定**: 调整PID参数或传感器高度
2. **转向不准确**: 重新校准IMU传感器
3. **速度不一致**: 检查编码器连接和电机性能

详细的故障排除指南请参考 [用户使用手册](docs/用户使用手册.md)

## 📊 性能指标

- **速度控制精度**: ±2%
- **角度控制精度**: ±1°
- **循迹精度**: ±5mm
- **响应时间**: <10ms
- **连续运行时间**: >30分钟

## 🤝 贡献指南

欢迎提交问题和改进建议！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 开发团队

- **架构设计**: Bob (系统架构师)
- **代码实现**: Alex (软件工程师)  
- **产品规划**: Emma (产品经理)
- **文档编写**: David (技术文档)
- **项目管理**: Mike (团队领袖)

## 📞 技术支持

如有技术问题，请：
1. 查阅项目文档
2. 检查常见问题解答
3. 提交 Issue 描述问题
4. 参与社区讨论

## 🏆 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

**⭐ 如果这个项目对你有帮助，请给我们一个星标！**
