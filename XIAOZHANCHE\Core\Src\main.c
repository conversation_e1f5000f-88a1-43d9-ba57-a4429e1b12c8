/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdint.h>
#include <stdio.h>
#include "Encoder.h"
#include "oled.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void xunji(void);
void key_work(void);
void mode_state(void);

// ==================== 状态机辅助函数声明 ====================
void handle_mode_10_idle(void);
void handle_mode_11_start(void);
void handle_mode_20_stop(void);
void handle_mode_21_23_line_follow(void);
void handle_mode_30_40_transition(void);
void handle_mode_31_45_turn_and_run(void);
void handle_mode_50_final(void);
bool is_line_detected(void);
bool is_angle_reached(int target_angle);
bool is_distance_reached(int threshold);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

// ==================== 全局变量定义 ====================
// 编码器相关变量
int16_t encoder_num_left = 0;        // 左轮编码器当前值
int16_t encoder_num_right = 0;       // 右轮编码器当前值
int16_t encoder_target_left = 0;     // 左轮编码器目标值
int16_t encoder_target_right = 0;    // 右轮编码器目标值
int encoder_count_left = 0;          // 左轮编码器累计计数
int encoder_count_right = 0;         // 右轮编码器累计计数

// PID控制器相关变量
PID_Increment_Struct PID_left, PID_right;    // PID控制器结构体
PID_Increment_Struct *PID_left1, *PID_right1; // PID控制器指针

// 电机PWM控制变量
int left_pwm = 0;                    // 左电机PWM值
int right_pwm = 0;                   // 右电机PWM值

// 系统状态和标志位
uint8_t printf_flag = 0;             // 打印标志位
uint8_t mode = 10;                   // 当前运行模式
uint8_t usart_num = 0;               // 串口数据计数
uint8_t usart_flag = 0;              // 串口数据接收标志
uint8_t xunji_flag = 0;              // 循迹标志位
uint8_t count_mode = 0;              // 模式计数器
uint8_t L_S_flag = 0;                // 激光标志位
uint8_t L_S_work = 0;                // 激光工作状态

// 串口通信相关变量
uint8_t usart_rx[30] = {0};          // 串口接收缓冲区
uint8_t rx_data = 0;                 // 串口接收单字节数据
uint8_t reset_str[] = {0xFF, 0xAA, 0x67}; // 复位指令

// 姿态角度相关变量
int yaw = 0;                         // 当前偏航角
int yaw0 = 0;                        // 目标偏航角
int yaw1 = 0;                        // 参考偏航角
float yaw_sourse = 0.0;              // 原始偏航角数据

// ==================== 常量定义 ====================
#define ENCODER_TIMER_PERIOD    5    // 编码器读取周期(ms)
#define LASER_WORK_TIME         250  // 激光工作时间(ms)
#define XUNJI_TIMEOUT           1000 // 循迹超时时间(ms)
#define YAW_ANGLE_TURN_38       38.6 // 转弯角度38.6度
#define YAW_ANGLE_TURN_143      143  // 转弯角度143度
#define YAW_ANGLE_TURN_180      180  // 转弯角度180度
#define ENCODER_COUNT_THRESHOLD 6800 // 编码器计数阈值
#define ENCODER_COUNT_THRESHOLD2 6700 // 编码器计数阈值2

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */
	
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_TIM2_Init();
  MX_TIM3_Init();
  MX_TIM4_Init();
  MX_USART1_UART_Init();
  /* USER CODE BEGIN 2 */

  // ==================== 硬件初始化 ====================

  // 启动编码器定时器 - 用于读取左右轮转速
  HAL_TIM_Encoder_Start(&htim3, TIM_CHANNEL_ALL);  // TIM3: 左轮编码器
  HAL_TIM_Encoder_Start(&htim4, TIM_CHANNEL_ALL);  // TIM4: 右轮编码器

  // 启动PWM定时器 - 用于控制电机转速
  HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_1);        // TIM2_CH1: 左电机PWM
  HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_2);        // TIM2_CH2: 右电机PWM
  HAL_TIM_Base_Start_IT(&htim2);                   // 启动TIM2基础定时器中断

  // ==================== PID控制器初始化 ====================

  // 左轮PID参数设置 (经过调试优化的参数)
  PID_left.Kp = 28.3407911607792;   // 比例系数
  PID_left.Ki = 4.5387430800153;    // 积分系数
  PID_left.Kd = 0;                  // 微分系数
  PID_left1 = &PID_left;            // 左轮PID控制器指针

  // 右轮PID参数设置 (右轮微分系数不同，用于补偿机械差异)
  PID_right.Kp = 28.3407911607792;  // 比例系数
  PID_right.Ki = 4.5387430800153;   // 积分系数
  PID_right.Kd = -0.663541586688759; // 微分系数(负值用于补偿)
  PID_right1 = &PID_right;          // 右轮PID控制器指针

  // ==================== 通信和显示初始化 ====================

  // 启动串口中断接收 - 用于接收姿态传感器数据
  HAL_UART_Receive_IT(&huart1, (uint8_t *)&rx_data, 1);

  // 初始化OLED显示屏
  OLED_Init();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // ==================== 主循环处理 ====================

    // 处理按键输入 - 检测模式切换和参数设置按键
    key_work();

    // 状态机处理 - 根据当前模式执行相应的控制逻辑
    mode_state();

    // ==================== 串口数据处理 ====================
    if(usart_flag)  // 当接收到完整的串口数据包时
    {
      usart_flag = 0;  // 清除串口数据标志

      // 解析姿态传感器数据包 (协议: 0x55 0x53 + 数据)
      if(usart_rx[0] == 0x55)  // 数据包头1
      {
        if(usart_rx[1] == 0x53)  // 数据包头2 (姿态数据包)
        {
          // 解析偏航角数据 (16位数据，范围-32768~32767对应-180°~180°)
          yaw_sourse = (usart_rx[7] << 8 | usart_rx[6]) * 180 / 32768;
          if(yaw_sourse)
            yaw = (int)yaw_sourse;  // 转换为整数角度
        }
      }

      // ==================== OLED显示更新 ====================
      // 显示当前偏航角
      OLED_ShowString(1, 1, "yaw:");
      OLED_ShowNum(1, 5, yaw, 4);

      // 显示目标偏航角
      OLED_ShowString(2, 1, "yaw0:");
      OLED_ShowNum(2, 6, yaw0, 4);

      // 显示参考偏航角
      OLED_ShowString(3, 1, "yaw1:");
      OLED_ShowNum(3, 6, yaw1, 4);

      // 显示当前运行模式
      OLED_ShowString(4, 1, "mode:");
      OLED_ShowNum(4, 6, mode, 3);
    }

    // ==================== 激光器控制 ====================
    if(L_S_work == 1)  // 激光器工作状态
    {
      HAL_GPIO_WritePin(L_S_GPIO_Port, L_S_Pin, GPIO_PIN_SET);    // 打开激光器
    }
    else
    {
      HAL_GPIO_WritePin(L_S_GPIO_Port, L_S_Pin, GPIO_PIN_RESET);  // 关闭激光器
    }

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

// ==================== 编码器读取函数 ====================

/**
 * @brief  读取左轮编码器数值
 * @note   读取TIM3编码器计数值并清零，用于计算左轮转速
 * @param  无
 * @retval 无
 */
void Get_num(void)  // 左轮编码器读取
{
  encoder_num_left = __HAL_TIM_GetCounter(&htim3);  // 获取TIM3编码器计数值
  __HAL_TIM_SetCounter(&htim3, 0);                  // 清零计数器，准备下次计数
}

/**
 * @brief  读取右轮编码器数值
 * @note   读取TIM4编码器计数值并清零，用于计算右轮转速
 * @param  无
 * @retval 无
 */
void Get_num2(void)  // 右轮编码器读取
{
  encoder_num_right = __HAL_TIM_GetCounter(&htim4); // 获取TIM4编码器计数值
  __HAL_TIM_SetCounter(&htim4, 0);                  // 清零计数器，准备下次计数
}

// ==================== 定时器相关变量 ====================
uint8_t encoder_timer = 0;      // 编码器读取定时器
uint8_t usart_timer = 0;         // 串口处理定时器
uint8_t L_S_timer = 0;           // 激光器工作定时器
uint16_t motor_time = 0;         // 电机控制定时器
uint16_t xunji_flag_timer = 0;   // 循迹超时定时器

/**
 * @brief  定时器周期中断回调函数
 * @note   TIM2中断回调，1ms周期，处理PID控制、传感器读取等实时任务
 * @param  htim: 定时器句柄指针
 * @retval 无
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == TIM2)  // TIM2定时器中断 (1ms周期)
  {
    // ==================== 激光器控制逻辑 ====================
    if(L_S_flag == 1)  // 激光器启动标志置位时
    {
      L_S_work = 1;    // 开始激光器工作
      L_S_timer++;     // 激光器工作时间计数

      if(L_S_timer >= LASER_WORK_TIME)  // 激光器工作时间到达(250ms)
      {
        L_S_timer = 0;  // 清零定时器
        L_S_flag = 0;   // 清除启动标志
        L_S_work = 0;   // 停止激光器工作
      }
    }

    // ==================== 运动控制逻辑 ====================
    // 根据不同模式和偏航角误差，设置左右轮目标速度

    if(mode == 11 || mode == 21 || mode == 23)  // 直线行驶模式
    {
      if(yaw < yaw0)  // 当前角度小于目标角度，需要左转
      {
        encoder_target_left = 20;   // 左轮稍慢
        encoder_target_right = 18;  // 右轮稍快，实现左转
      }
      else if(yaw > yaw0)  // 当前角度大于目标角度，需要右转
      {
        encoder_target_left = 18;   // 左轮稍快
        encoder_target_right = 20;  // 右轮稍慢，实现右转
      }
      else if(yaw == yaw0)  // 角度正确，直线行驶
      {
        encoder_target_left = 20;   // 左右轮同速
        encoder_target_right = 20;
      }
    }
    else if(mode == 31 || mode == 35 || mode == 41 || mode == 45)  // 原地转向模式
    {
      if(yaw < yaw0)  // 需要左转
      {
        encoder_target_left = 3;    // 左轮前进
        encoder_target_right = -3;  // 右轮后退，实现原地左转
      }
      else if(yaw > yaw0)  // 需要右转
      {
        encoder_target_left = -3;   // 左轮后退
        encoder_target_right = 3;   // 右轮前进，实现原地右转
      }
      else if(yaw == yaw0)  // 角度到达，停止转向
      {
        encoder_target_left = 0;    // 停止
        encoder_target_right = 0;
      }
    }
    else if(mode == 33 || mode == 37 || mode == 43 || mode == 47)  // 慢速转向模式
    {
      if(yaw < yaw0)  // 需要左转
      {
        encoder_target_left = 10;   // 左轮慢速
        encoder_target_right = 1;   // 右轮极慢，实现大半径左转
      }
      else if(yaw > yaw0)  // 需要右转
      {
        encoder_target_left = 1;    // 左轮极慢
        encoder_target_right = 10;  // 右轮慢速，实现大半径右转
      }
      else if(yaw == yaw0)  // 角度正确，直线行驶
      {
        encoder_target_left = 10;   // 左右轮同速慢行
        encoder_target_right = 10;
      }
    }
    else if(mode == 32 || mode == 36 || mode == 42 || mode == 46)  // 快速直线模式
    {
      if(yaw < yaw0)  // 需要左转修正
      {
        encoder_target_left = 32;   // 左轮快速
        encoder_target_right = 25;  // 右轮稍慢，实现快速左转修正
      }
      else if(yaw > yaw0)  // 需要右转修正
      {
        encoder_target_left = 25;   // 左轮稍慢
        encoder_target_right = 32;  // 右轮快速，实现快速右转修正
      }
      else  // 角度正确，快速直线行驶
      {
        encoder_target_left = 30;   // 左右轮高速同步
        encoder_target_right = 30;
      }
    }
    // ==================== PID控制周期处理 ====================
    encoder_timer++;  // 编码器读取定时器递增

    if(encoder_timer >= ENCODER_TIMER_PERIOD)  // 每5ms执行一次PID控制
    {
      encoder_timer = 0;  // 清零编码器定时器
      usart_timer = 0;    // 清零串口定时器
      usart_flag = 1;     // 设置串口处理标志

      // 读取左右轮编码器数值
      Get_num();   // 读取左轮编码器
      Get_num2();  // 读取右轮编码器

      // 在特定模式下累计编码器计数，用于距离控制
      if(mode == 32 || mode == 36 || mode == 42 || mode == 46)
      {
        encoder_count_left += encoder_num_left;    // 累计左轮编码器计数
        encoder_count_right += encoder_num_right;  // 累计右轮编码器计数
      }

      // ==================== 左轮PID控制 ====================
      if(encoder_target_left != 0)  // 左轮有目标速度时
      {
        // 使用PID算法计算左轮PWM输出
        left_pwm = (int)PID_Increment(PID_left1, encoder_num_left, encoder_target_left);
      }
      else  // 左轮目标速度为0时
      {
        left_pwm = 0;  // 直接停止左轮
      }

      // ==================== 右轮PID控制 ====================
      if(encoder_target_right != 0)  // 右轮有目标速度时
      {
        // 使用PID算法计算右轮PWM输出
        right_pwm = (int)PID_Increment(PID_right1, encoder_num_right, encoder_target_right);
      }
      else  // 右轮目标速度为0时
      {
        right_pwm = 0;  // 直接停止右轮
      }

      // ==================== 电机PWM输出 ====================
      set_pwm_left(left_pwm);    // 设置左电机PWM
      set_pwm_right(right_pwm);  // 设置右电机PWM
    }
    // ==================== 循迹超时处理 ====================
    if(xunji_flag)  // 循迹标志置位时
    {
      xunji_flag_timer++;  // 循迹超时计时器递增

      if(xunji_flag_timer >= XUNJI_TIMEOUT)  // 循迹超时(1000ms)
      {
        xunji_flag_timer = 0;  // 清零超时计时器
        xunji_flag = 0;        // 清除循迹标志，防止长时间卡在循迹状态
      }
    }
  }
}

/**
 * @brief  循迹控制函数
 * @note   根据7路红外传感器的状态，调整左右轮速度实现循迹
 *         传感器排列: G1(最左) G2 G3 G4(中间) G5 G6 G7(最右)
 *         循迹原理: 检测到黑线的传感器输出1，白色区域输出0
 *         控制策略: 线偏左时右轮快左轮慢，线偏右时左轮快右轮慢
 * @param  无
 * @retval 无
 */
void xunji(void)
{
  // ==================== 左侧偏离处理 ====================
  if(G1==1 && G2==0 && G3==0 && G4==0 && G5==0 && G6==0 && G7==0)  // 线在最左侧
  {
    encoder_target_right = 1;   // 右轮极慢速
    encoder_target_left = 30;   // 左轮正常速度，实现大幅右转
  }
  if(G1==1 && G2==1 && G3==0 && G4==0 && G5==0 && G6==0 && G7==0)  // 线在左侧偏外
  {
    encoder_target_right = 5;   // 右轮慢速
    encoder_target_left = 30;   // 左轮正常速度，实现中等右转
  }
  if(G1==0 && G2==1 && G3==0 && G4==0 && G5==0 && G6==0 && G7==0)  // 线在左侧
  {
    encoder_target_right = 10;  // 右轮中慢速
    encoder_target_left = 30;   // 左轮正常速度，实现小幅右转
  }
  if(G1==0 && G2==1 && G3==1 && G4==0 && G5==0 && G6==0 && G7==0)  // 线在左侧偏内
  {
    encoder_target_right = 15;  // 右轮中速
    encoder_target_left = 30;   // 左轮正常速度，实现微调右转
  }
  if(G1==0 && G2==0 && G3==1 && G4==0 && G5==0 && G6==0 && G7==0)  // 线在左中位置
  {
    encoder_target_right = 20;  // 右轮中高速
    encoder_target_left = 30;   // 左轮正常速度，实现轻微右转
  }
  if(G1==0 && G2==0 && G3==1 && G4==1 && G5==0 && G6==0 && G7==0)  // 线在中左位置
  {
    encoder_target_right = 25;  // 右轮较高速
    encoder_target_left = 30;   // 左轮正常速度，实现微调
  }

  // ==================== 中间位置处理 ====================
  if(G1==0 && G2==0 && G3==0 && G4==1 && G5==0 && G6==0 && G7==0)  // 线在正中间
  {
    encoder_target_right = 30;  // 左右轮同速
    encoder_target_left = 30;   // 直线前进
  }

  // ==================== 右侧偏离处理 ====================
  if(G1==0 && G2==0 && G3==0 && G4==1 && G5==1 && G6==0 && G7==0)  // 线在中右位置
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 25;   // 左轮较高速，实现微调
  }
  if(G1==0 && G2==0 && G3==0 && G4==0 && G5==1 && G6==0 && G7==0)  // 线在右中位置
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 20;   // 左轮中高速，实现轻微左转
  }
  if(G1==0 && G2==0 && G3==0 && G4==0 && G5==1 && G6==1 && G7==0)  // 线在右侧偏内
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 15;   // 左轮中速，实现微调左转
  }
  if(G1==0 && G2==0 && G3==0 && G4==0 && G5==0 && G6==1 && G7==0)  // 线在右侧
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 10;   // 左轮中慢速，实现小幅左转
  }
  if(G1==0 && G2==0 && G3==0 && G4==0 && G5==0 && G6==1 && G7==1)  // 线在右侧偏外
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 5;    // 左轮慢速，实现中等左转
  }
  if(G1==0 && G2==0 && G3==0 && G4==0 && G5==0 && G6==0 && G7==1)  // 线在最右侧
  {
    encoder_target_right = 30;  // 右轮正常速度
    encoder_target_left = 1;    // 左轮极慢速，实现大幅左转
  }
}

void mode_state(void)
{
	if(mode==10)
	{
		
	}
	else if(mode==11)
	{
//		encoder_target_right=20;
//		encoder_target_left=20;
		yaw0=yaw1;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			encoder_target_right=1;
			encoder_target_left=1;
			mode=20;
			L_S_flag=1;
		}
	}
	else if(mode==20)
	{
		if(encoder_num_right==1)
			encoder_target_right=0;
		if(encoder_num_left==1)
		encoder_target_left=0;
	}
	else if(mode==21)
	{
//		encoder_target_right=20;
//		encoder_target_left=20;
		yaw0=yaw1;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			mode=22;
			L_S_flag=1;
		}
	}
	else if(mode==22)
	{
		xunji();
		if(!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)
		{
			mode=23;
			yaw0=yaw0-180;
			L_S_flag=1;
		}
	}
	else if(mode==23)
	{
//		encoder_target_right=20;
//		encoder_target_left=20;
		
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			mode=24;
			L_S_flag=1;
		}
	}
	else if(mode==24)
	{
		xunji();
		if(!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)
		{
			mode=30;
			encoder_target_right=1;
			encoder_target_left=1;
			L_S_flag=1;
		}
	}
	else if(mode==30)
	{
		if(encoder_num_right==1)
			encoder_target_right=0;
		if(encoder_num_left==1)
		encoder_target_left=0;
	}
	else if(mode==31)
	{
		yaw0=yaw1-38.6;
		if(yaw==yaw0)
		{
			mode=32;
		}
	}
	else if(mode==32)
	{
		if(encoder_count_left>=6800||encoder_count_right>=6800)
		{
			mode=33;
			encoder_count_left=0;
			encoder_count_right=0;
//			xunji_flag=1;
		}
//		if(G1||G2||G3||G4||G5||G6||G7)
//		{
//			mode=33;
//			xunji_flag=1;
//		}
	}
	else if(mode==33)
	{
		yaw0=yaw1;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			mode=34;
			xunji_flag=1;
			L_S_flag=1;
		}
	}
	else if(mode==34)
	{
		xunji();
		if((!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)&&(xunji_flag==0))
		{
			encoder_target_right=-1;
			encoder_target_left=-1;
			
			mode=35;
			yaw0=yaw1-143;
			L_S_flag=1;
		}
	}
	else if(mode==35)
	{
		if(yaw==yaw0)
		{
			mode=36;
		}
	}
	else if(mode==36)
	{
		
		if(encoder_count_left>=6700||encoder_count_right>=6700)
		{
			mode=37;
			encoder_count_left=0;
			encoder_count_right=0;
//			xunji_flag=1;
		}

//		if(G1||G2||G3||G4||G5||G6||G7)
//		{
//			mode=37;
//			xunji_flag=1;
//		}
	}
	else if(mode==37)
	{
		yaw0=yaw1-180;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			mode=38;
			xunji_flag=1;
			L_S_flag=1;
		}
	}
	else if(mode==38)
	{
		xunji();
		if((!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)&&(xunji_flag==0))
		{
			mode=40;
			encoder_target_right=-1;
			encoder_target_left=-1;
			L_S_flag=1;
		}
	}
	else if(mode==40)
	{
		if(encoder_num_right==-1)
			encoder_target_right=0;
		if(encoder_num_left==-1)
		encoder_target_left=0;
	}
	else if(mode==41)
	{
		yaw0=yaw1-38.6;
		if(yaw==yaw0)
		{
			mode=42;
		}
	}
	else if(mode==42)
	{
		if(encoder_count_left>=6800||encoder_count_right>=6800)
		{
			mode=43;
			encoder_count_left=0;
			encoder_count_right=0;
//			xunji_flag=1;
		}
	}
	else if(mode==43)
	{
		yaw0=yaw1;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			mode=44;
			xunji_flag=1;
			L_S_flag=1;
		}
	}
	else if(mode==44)
	{
		xunji();
		if((!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)&&(xunji_flag==0))
		{
			encoder_target_right=-1;
			encoder_target_left=-1;
			L_S_flag=1;
			
			yaw0=yaw1-143;
			mode=45;
		}
	}
	else if(mode==45)
	{
		if(yaw==yaw0)
		{
			mode=46;
		}
	}
	else if(mode==46)
	{
		if(encoder_count_left>=6700||encoder_count_right>=6700)
		{
			
			encoder_count_left=0;
			encoder_count_right=0;
			mode=47;
//			xunji_flag=1;
		}
	}
	else if(mode==47)
	{
		yaw0=yaw1-180;
		if(G1||G2||G3||G4||G5||G6||G7)
		{
			
			xunji_flag=1;
			L_S_flag=1;
			mode=48;
		}
	}
	else if(mode==48)
	{
		xunji();
		if((!G1&&!G2&&!G3&&!G4&&!G5&&!G6&&!G7)&&(xunji_flag==0))
		{
			encoder_target_right=-1;
			encoder_target_left=-1;
			L_S_flag=1;
			count_mode++;
			if(count_mode>=4)
			{
				mode=50;
			}
			else
			{
				mode=41;
				yaw0=yaw1-38.6;
			}
			
//			yaw0=yaw1-38.6;
		}
		
	}
	else if(mode==50)
	{
		if(encoder_num_right==-1)
			encoder_target_right=0;
		if(encoder_num_left==-1)
			encoder_target_left=0;
	}
}

/**
 * @brief  按键处理函数
 * @note   处理三个功能按键的输入，实现模式切换、角度设置和传感器复位
 *         按键采用低电平触发，包含消抖处理
 * @param  无
 * @retval 无
 */
void key_work(void)
{
  // ==================== 模式切换按键处理 ====================
  if(Key_mode == 0)  // 模式按键按下 (低电平触发)
  {
    HAL_Delay(20);   // 消抖延时20ms
    if(Key_mode == 0)  // 再次确认按键状态，确保不是干扰
    {
      while(Key_mode == 0);  // 等待按键释放，防止连续触发

      if(mode % 10 == 0)  // 只有在模式的个位数为0时才允许切换
      {
        mode = mode + 1;  // 模式递增，如10->11, 20->21
      }
    }
  }

  // ==================== 偏航角设置按键处理 ====================
  if(Key_yaw == 0)  // 偏航角设置按键按下
  {
    HAL_Delay(20);   // 消抖延时20ms
    if(Key_yaw == 0)  // 再次确认按键状态
    {
      while(Key_yaw == 0);  // 等待按键释放
      yaw1 = yaw;           // 将当前偏航角设为参考角度
    }
  }

  // ==================== 传感器复位按键处理 ====================
  if(Key_reset == 0)  // 复位按键按下
  {
    HAL_Delay(20);    // 消抖延时20ms
    if(Key_reset == 0)  // 再次确认按键状态
    {
      while(Key_reset == 0);  // 等待按键释放
      // 发送复位指令到姿态传感器 (0xFF 0xAA 0x67)
      HAL_UART_Transmit(&huart1, reset_str, 3, 100);
    }
  }
}

// ==================== 状态机辅助函数实现 ====================

/**
 * @brief  检测是否有循迹线
 * @retval true: 检测到线, false: 未检测到线
 */
bool is_line_detected(void)
{
  return (G1 || G2 || G3 || G4 || G5 || G6 || G7);
}

/**
 * @brief  检测角度是否到达目标值
 * @param  target_angle: 目标角度
 * @retval true: 角度到达, false: 角度未到达
 */
bool is_angle_reached(int target_angle)
{
  return (yaw == target_angle);
}

/**
 * @brief  检测距离是否到达阈值
 * @param  threshold: 距离阈值
 * @retval true: 距离到达, false: 距离未到达
 */
bool is_distance_reached(int threshold)
{
  return (encoder_count_left >= threshold || encoder_count_right >= threshold);
}

/**
 * @brief  处理模式10 - 空闲状态
 */
void handle_mode_10_idle(void)
{
  // 空闲状态，等待按键启动
}

/**
 * @brief  处理模式11 - 启动状态
 */
void handle_mode_11_start(void)
{
  yaw0 = yaw1;  // 设置目标角度为参考角度

  if(is_line_detected())  // 检测到循迹线
  {
    encoder_target_right = 1;  // 设置低速前进
    encoder_target_left = 1;
    mode = 20;                 // 切换到停止状态
    L_S_flag = 1;              // 启动激光器
  }
}

/**
 * @brief  处理模式20 - 停止状态
 */
void handle_mode_20_stop(void)
{
  // 检测编码器值，实现精确停止
  if(encoder_num_right == 1)
    encoder_target_right = 0;
  if(encoder_num_left == 1)
    encoder_target_left = 0;
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
