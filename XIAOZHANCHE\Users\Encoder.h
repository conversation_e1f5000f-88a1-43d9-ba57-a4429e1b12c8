/**
 * @file    Encoder.h
 * @brief   编码器和电机控制模块头文件
 * @note    包含PID控制器、电机驱动、数值处理等功能的声明
 * <AUTHOR>
 * @date    2024
 */

#ifndef _ENCODER_H_
#define _ENCODER_H_

#include "main.h"
#include "tim.h"

// ==================== 电机方向控制宏定义 ====================

/**
 * @brief 左电机后退方向控制
 * @note  MOTOR_1=0, MOTOR_2=1 实现左电机反转
 */
#define left_BACK HAL_GPIO_WritePin(MOTOR_1_GPIO_Port,MOTOR_1_Pin,GPIO_PIN_RESET),HAL_GPIO_WritePin(MOTOR_2_GPIO_Port,MOTOR_2_Pin,GPIO_PIN_SET)

/**
 * @brief 右电机后退方向控制
 * @note  MOTOR_3=1, MOTOR_4=0 实现右电机反转
 */
#define right_BACK HAL_GPIO_WritePin(MOTOR_3_GPIO_Port,MOTOR_3_Pin,GPIO_PIN_SET),HAL_GPIO_WritePin(MOTOR_4_GPIO_Port,MOTOR_4_Pin,GPIO_PIN_RESET)

/**
 * @brief 左电机前进方向控制
 * @note  MOTOR_1=1, MOTOR_2=0 实现左电机正转
 */
#define left_GO HAL_GPIO_WritePin(MOTOR_1_GPIO_Port,MOTOR_1_Pin,GPIO_PIN_SET),HAL_GPIO_WritePin(MOTOR_2_GPIO_Port,MOTOR_2_Pin,GPIO_PIN_RESET)

/**
 * @brief 右电机前进方向控制
 * @note  MOTOR_3=0, MOTOR_4=1 实现右电机正转
 */
#define right_GO HAL_GPIO_WritePin(MOTOR_3_GPIO_Port,MOTOR_3_Pin,GPIO_PIN_RESET),HAL_GPIO_WritePin(MOTOR_4_GPIO_Port,MOTOR_4_Pin,GPIO_PIN_SET)

// ==================== PID控制器结构体定义 ====================

/**
 * @brief 增量式PID控制器结构体
 * @note  用于电机速度控制的PID参数和历史数据存储
 */
typedef struct __PID_Increment_Struct
{
    float Kp, Ki, Kd;  // PID控制参数: 比例、积分、微分系数
    float Error_Last1; // 上一次误差值 e(k-1)
    float Error_Last2; // 上上次误差值 e(k-2)
    float Out_Last;    // 上一次输出值，用于增量式计算
} PID_Increment_Struct;

// ==================== 函数声明 ====================

// PID控制相关函数
float PID_Increment(PID_Increment_Struct *PID, float Current, float Target);  // 增量式PID控制器
int TLY_PID(int position, int target);  // 位置式PID控制器 (备用)

// 数值处理工具函数
int myabs(int num);                      // 整数绝对值函数
int Xianfu(int num, int value);          // 整数限幅函数
float Xianfu_float(float num, int value); // 浮点数限幅函数

// 电机控制函数
void set_pwm_left(int num);              // 设置左电机PWM和方向
void set_pwm_right(int num);             // 设置右电机PWM和方向

#endif
