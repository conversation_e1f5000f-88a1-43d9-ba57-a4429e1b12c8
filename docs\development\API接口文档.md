# STM32智能小车 API接口文档

## 1. 概述

本文档详细描述了STM32智能小车项目中所有公开函数的接口规范，包括参数说明、返回值、使用示例等。

## 2. PID控制模块 (Encoder.c/h)

### 2.1 PID_Increment()
**功能**: 增量式PID控制算法实现

```c
float PID_Increment(PID_Increment_Struct *PID, float Current, float Target);
```

**参数**:
- `PID`: PID控制器结构体指针
- `Current`: 当前实际值 (编码器反馈值)
- `Target`: 目标设定值 (期望速度)

**返回值**: PID控制器输出值 (PWM占空比)

**使用示例**:
```c
// 初始化PID参数
PID_left.Kp = 28.34;
PID_left.Ki = 4.54;
PID_left.Kd = 0;

// 计算PID输出
float pwm_output = PID_Increment(&PID_left, encoder_feedback, target_speed);
```

**注意事项**:
- 输出值自动限幅在±2000范围内
- 结构体中的历史误差会自动更新
- 适用于速度控制，不适用于位置控制

### 2.2 set_pwm_left()
**功能**: 设置左电机PWM和方向

```c
void set_pwm_left(int num);
```

**参数**:
- `num`: PWM值 (带符号，范围±2000)
  - 正值: 前进方向
  - 负值: 后退方向
  - 0: 停止

**使用示例**:
```c
set_pwm_left(1500);   // 左电机前进，PWM=1500
set_pwm_left(-800);   // 左电机后退，PWM=800
set_pwm_left(0);      // 左电机停止
```

### 2.3 set_pwm_right()
**功能**: 设置右电机PWM和方向

```c
void set_pwm_right(int num);
```

**参数**:
- `num`: PWM值 (带符号，范围±2000)

**使用方法**: 与`set_pwm_left()`相同

### 2.4 myabs()
**功能**: 计算整数绝对值

```c
int myabs(int num);
```

**参数**:
- `num`: 输入整数

**返回值**: 输入数的绝对值

### 2.5 Xianfu()
**功能**: 整数限幅函数

```c
int Xianfu(int num, int value);
```

**参数**:
- `num`: 需要限幅的数值
- `value`: 限幅范围 (绝对值)

**返回值**: 限幅后的数值，范围[-value, +value]

### 2.6 Xianfu_float()
**功能**: 浮点数限幅函数

```c
float Xianfu_float(float num, int value);
```

**参数**:
- `num`: 需要限幅的浮点数
- `value`: 限幅范围 (整数)

**返回值**: 限幅后的浮点数

## 3. 主控制模块 (main.c)

### 3.1 Get_num()
**功能**: 读取左轮编码器数值

```c
void Get_num(void);
```

**功能说明**:
- 读取TIM3编码器计数值
- 自动清零计数器
- 结果存储在全局变量`encoder_num_left`中

### 3.2 Get_num2()
**功能**: 读取右轮编码器数值

```c
void Get_num2(void);
```

**功能说明**:
- 读取TIM4编码器计数值
- 自动清零计数器
- 结果存储在全局变量`encoder_num_right`中

### 3.3 xunji()
**功能**: 循迹控制算法

```c
void xunji(void);
```

**功能说明**:
- 根据7路红外传感器状态调整电机速度
- 自动设置`encoder_target_left`和`encoder_target_right`
- 实现基于传感器反馈的循迹控制

**传感器状态对应**:
```c
G1=1, others=0  -> 线在最左侧，大幅右转
G4=1, others=0  -> 线在中间，直线前进
G7=1, others=0  -> 线在最右侧，大幅左转
```

### 3.4 key_work()
**功能**: 按键处理函数

```c
void key_work(void);
```

**功能说明**:
- 处理三个功能按键输入
- 包含20ms消抖处理
- 支持模式切换、角度设置、传感器复位

**按键功能**:
- `Key_mode`: 模式切换 (仅在模式个位数为0时有效)
- `Key_yaw`: 设置当前角度为参考角度
- `Key_reset`: 发送IMU复位指令

### 3.5 mode_state()
**功能**: 状态机处理函数

```c
void mode_state(void);
```

**功能说明**:
- 根据当前模式执行相应控制逻辑
- 管理状态转换和任务流程
- 处理复杂的导航和路径规划

## 4. 状态机辅助函数

### 4.1 is_line_detected()
**功能**: 检测是否有循迹线

```c
bool is_line_detected(void);
```

**返回值**:
- `true`: 检测到循迹线
- `false`: 未检测到循迹线

### 4.2 is_angle_reached()
**功能**: 检测角度是否到达目标值

```c
bool is_angle_reached(int target_angle);
```

**参数**:
- `target_angle`: 目标角度

**返回值**:
- `true`: 角度已到达
- `false`: 角度未到达

### 4.3 is_distance_reached()
**功能**: 检测距离是否到达阈值

```c
bool is_distance_reached(int threshold);
```

**参数**:
- `threshold`: 距离阈值 (编码器计数)

**返回值**:
- `true`: 距离已到达
- `false`: 距离未到达

## 5. 全局变量说明

### 5.1 编码器相关变量
```c
int16_t encoder_num_left;      // 左轮编码器当前值
int16_t encoder_num_right;     // 右轮编码器当前值
int16_t encoder_target_left;   // 左轮编码器目标值
int16_t encoder_target_right;  // 右轮编码器目标值
int encoder_count_left;        // 左轮编码器累计计数
int encoder_count_right;       // 右轮编码器累计计数
```

### 5.2 PID控制器变量
```c
PID_Increment_Struct PID_left, PID_right;      // PID控制器结构体
PID_Increment_Struct *PID_left1, *PID_right1;  // PID控制器指针
```

### 5.3 系统状态变量
```c
uint8_t mode;          // 当前运行模式
int yaw, yaw0, yaw1;   // 偏航角相关变量
uint8_t L_S_flag;      // 激光器标志位
```

## 6. 宏定义说明

### 6.1 传感器读取宏
```c
#define G1 HAL_GPIO_ReadPin(G1_GPIO_Port,G1_Pin)  // 最左侧循迹传感器
#define G4 HAL_GPIO_ReadPin(G4_GPIO_Port,G4_Pin)  // 中间循迹传感器
#define G7 HAL_GPIO_ReadPin(G7_GPIO_Port,G7_Pin)  // 最右侧循迹传感器
```

### 6.2 按键读取宏
```c
#define Key_mode HAL_GPIO_ReadPin(Key_mode_GPIO_Port,Key_mode_Pin)    // 模式按键
#define Key_yaw HAL_GPIO_ReadPin(Key_yaw_GPIO_Port,Key_yaw_Pin)       // 角度按键
#define Key_reset HAL_GPIO_ReadPin(Key_reset_GPIO_Port,Key_reset_Pin) // 复位按键
```

### 6.3 电机控制宏
```c
#define left_GO    // 左电机前进
#define left_BACK  // 左电机后退
#define right_GO   // 右电机前进
#define right_BACK // 右电机后退
```

## 7. 使用注意事项

### 7.1 初始化顺序
1. 硬件初始化 (GPIO, TIM, UART)
2. PID参数设置
3. 全局变量初始化
4. 启动定时器中断

### 7.2 实时性要求
- PID控制周期: 5ms
- 传感器读取: 1ms
- 状态机更新: 实时

### 7.3 安全考虑
- PWM输出自动限幅
- 编码器数据溢出保护
- 按键消抖处理
- 异常状态检测
