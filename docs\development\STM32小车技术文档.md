# STM32智能小车技术文档

## 1. 项目概述

### 1.1 项目简介
本项目是基于STM32F103微控制器的智能小车系统，具备循迹、姿态控制、运动控制等功能。主要用于电子设计竞赛，实现复杂的自主导航和精确控制。

### 1.2 技术特点
- **高精度控制**: 采用增量式PID算法，实现精确的速度和位置控制
- **多传感器融合**: 集成7路红外循迹传感器和IMU姿态传感器
- **实时响应**: 1ms级别的控制周期，保证系统实时性
- **模块化设计**: 代码结构清晰，便于维护和扩展

## 2. 硬件架构

### 2.1 主控制器
- **型号**: STM32F103C8T6
- **主频**: 72MHz
- **Flash**: 64KB
- **RAM**: 20KB

### 2.2 传感器模块
#### 2.2.1 循迹传感器阵列
- **数量**: 7路红外传感器
- **排列**: G1(最左) - G2 - G3 - G4(中间) - G5 - G6 - G7(最右)
- **检测原理**: 红外反射式，黑线吸收红外光，白色区域反射红外光
- **输出**: 数字信号，1=检测到黑线，0=检测到白色

#### 2.2.2 姿态传感器
- **类型**: IMU惯性测量单元
- **通信**: UART串口通信
- **数据格式**: 0x55 0x53 + 姿态数据
- **精度**: 16位数据，±180°范围

### 2.3 执行器模块
#### 2.3.1 电机驱动系统
- **电机类型**: 直流减速电机
- **驱动方式**: H桥驱动，支持正反转
- **控制信号**: PWM调速 + 方向控制
- **编码器**: 增量式编码器，用于速度反馈

#### 2.3.2 显示模块
- **类型**: OLED显示屏
- **通信**: I2C接口
- **功能**: 实时显示系统状态和调试信息

### 2.4 人机交互
- **按键**: 3个功能按键（模式切换、角度设置、复位）
- **激光器**: 用于标记和指示

## 3. 软件架构

### 3.1 系统架构图
```
┌─────────────────────────────────────────┐
│            应用逻辑层                    │
│  ┌─────────────┐  ┌─────────────────────┐ │
│  │ 状态机管理   │  │   按键处理           │ │
│  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           控制算法层                     │
│  ┌─────────────┐  ┌─────────────────────┐ │
│  │ PID控制器   │  │   循迹算法           │ │
│  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│          硬件抽象层                      │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │编码器   │ │电机驱动 │ │ 传感器读取   │ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
```

### 3.2 核心模块

#### 3.2.1 PID控制模块 (Encoder.c/h)
**功能**: 实现增量式PID控制算法
**核心函数**:
- `PID_Increment()`: 增量式PID计算
- `set_pwm_left()`: 左电机PWM设置
- `set_pwm_right()`: 右电机PWM设置

**PID参数**:
- 左轮: Kp=28.34, Ki=4.54, Kd=0
- 右轮: Kp=28.34, Ki=4.54, Kd=-0.66

#### 3.2.2 状态机模块 (main.c)
**功能**: 管理小车的运行状态和模式切换
**主要状态**:
- 模式10: 空闲状态
- 模式11-24: 循迹和导航
- 模式31-48: 复杂路径规划
- 模式50: 任务完成

#### 3.2.3 传感器处理模块
**循迹传感器**: `xunji()` 函数实现7路传感器的循迹算法
**姿态传感器**: 串口中断接收IMU数据，实时更新偏航角

## 4. 控制算法

### 4.1 PID控制算法
#### 4.1.1 增量式PID公式
```
Δu(k) = Kp*[e(k)-e(k-1)] + Ki*e(k) + Kd*[e(k)-2*e(k-1)+e(k-2)]
u(k) = u(k-1) + Δu(k)
```

#### 4.1.2 参数调优
- **比例系数(Kp)**: 影响系统响应速度，过大会振荡
- **积分系数(Ki)**: 消除稳态误差，过大会超调
- **微分系数(Kd)**: 改善动态性能，抑制振荡

### 4.2 循迹算法
#### 4.2.1 传感器状态解析
- 单传感器触发: 线偏离，需要转向修正
- 中间传感器触发: 直线行驶
- 多传感器触发: 交叉路口或宽线

#### 4.2.2 速度分配策略
```c
// 左偏时右转: 左轮慢，右轮快
if(线在左侧) {
    left_speed = 低速;
    right_speed = 正常速度;
}
```

### 4.3 姿态控制算法
#### 4.3.1 角度误差计算
```c
angle_error = target_yaw - current_yaw;
```

#### 4.3.2 差速控制
```c
if(angle_error < 0) {  // 需要左转
    left_speed = base_speed - correction;
    right_speed = base_speed + correction;
}
```

## 5. 通信协议

### 5.1 IMU数据协议
**数据包格式**:
- 包头: 0x55 0x53
- 数据: 16位偏航角数据
- 转换: `yaw = (data * 180) / 32768`

### 5.2 复位指令
**指令格式**: 0xFF 0xAA 0x67
**功能**: 复位IMU传感器的角度基准

## 6. 性能指标

### 6.1 控制精度
- **速度控制精度**: ±2%
- **角度控制精度**: ±1°
- **循迹精度**: ±5mm

### 6.2 响应时间
- **PID控制周期**: 5ms
- **传感器采样**: 1ms
- **状态机更新**: 实时

### 6.3 系统稳定性
- **连续运行时间**: >30分钟
- **抗干扰能力**: 良好
- **温度适应性**: 0-50°C

## 7. 调试和测试

### 7.1 调试接口
- **OLED显示**: 实时显示关键参数
- **串口输出**: 调试信息输出
- **LED指示**: 状态指示

### 7.2 测试方法
- **单元测试**: 各模块独立测试
- **集成测试**: 整体功能验证
- **性能测试**: 精度和稳定性测试

## 8. 故障排除

### 8.1 常见问题
1. **循迹不稳定**: 检查传感器高度和PID参数
2. **转向不准确**: 校准IMU传感器和角度参数
3. **速度不一致**: 检查电机和编码器连接

### 8.2 维护建议
- 定期清洁传感器
- 检查电机和编码器
- 更新PID参数
- 备份重要配置
