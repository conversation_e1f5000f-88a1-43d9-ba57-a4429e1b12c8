# 智能小车控制模块移植指南

## 🎯 移植目标

将STM32智能小车的控制逻辑移植到其他平台或项目中，实现：
- PID电机控制
- 循迹算法
- 姿态控制
- 运动控制

## 📋 移植清单

### 必需文件
- ✅ `robot_control.h` - 控制模块头文件
- ✅ `robot_control.c` - 控制模块实现
- ✅ `robot_control_example.c` - 使用示例 (可选)

### 依赖项
- `stdint.h` - 标准整数类型
- `stdbool.h` - 布尔类型
- `string.h` - 字符串操作 (memset等)

## 🚀 5分钟快速移植

### 步骤1: 复制文件 (1分钟)
```bash
# 将以下文件复制到你的项目中
robot_control.h
robot_control.c
```

### 步骤2: 添加到项目 (1分钟)
```c
// 在你的主文件中包含
#include "robot_control.h"
```

### 步骤3: 实现硬件接口 (2分钟)
```c
// 实现三个回调函数
void Motor_PWM_Callback(uint8_t motor_id, int16_t pwm, bool direction) {
    // 你的电机控制代码
}

void Sensor_Read_Callback(uint8_t sensor_id, uint8_t *value) {
    // 你的传感器读取代码
}

void Attitude_Read_Callback(float *yaw) {
    // 你的姿态传感器读取代码
}
```

### 步骤4: 初始化和使用 (1分钟)
```c
int main(void) {
    // 初始化
    RobotControl_Init();
    
    // 注册回调
    RobotControl_RegisterMotorCallback(Motor_PWM_Callback);
    RobotControl_RegisterSensorCallback(Sensor_Read_Callback);
    RobotControl_RegisterAttitudeCallback(Attitude_Read_Callback);
    
    // 控制循环
    while(1) {
        // 你的控制逻辑
        HAL_Delay(5);
    }
}
```

## 🔧 平台适配

### STM32平台
```c
// 电机控制 - STM32 HAL
void Motor_PWM_Callback(uint8_t motor_id, int16_t pwm, bool direction) {
    if (motor_id == 0) {  // 左电机
        HAL_GPIO_WritePin(MOTOR_1_GPIO_Port, MOTOR_1_Pin, direction ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(MOTOR_2_GPIO_Port, MOTOR_2_Pin, direction ? GPIO_PIN_RESET : GPIO_PIN_SET);
        __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_1, pwm);
    }
    // 右电机类似...
}

// 传感器读取 - STM32 HAL
void Sensor_Read_Callback(uint8_t sensor_id, uint8_t *value) {
    GPIO_TypeDef* ports[] = {G1_GPIO_Port, G2_GPIO_Port, G3_GPIO_Port, G4_GPIO_Port, G5_GPIO_Port, G6_GPIO_Port, G7_GPIO_Port};
    uint16_t pins[] = {G1_Pin, G2_Pin, G3_Pin, G4_Pin, G5_Pin, G6_Pin, G7_Pin};
    
    if (sensor_id < 7) {
        *value = HAL_GPIO_ReadPin(ports[sensor_id], pins[sensor_id]);
    }
}
```

### Arduino平台
```c
// 电机控制 - Arduino
void Motor_PWM_Callback(uint8_t motor_id, int16_t pwm, bool direction) {
    if (motor_id == 0) {  // 左电机
        digitalWrite(MOTOR_1_PIN, direction ? HIGH : LOW);
        digitalWrite(MOTOR_2_PIN, direction ? LOW : HIGH);
        analogWrite(PWM_LEFT_PIN, map(pwm, 0, 2000, 0, 255));
    }
    // 右电机类似...
}

// 传感器读取 - Arduino
void Sensor_Read_Callback(uint8_t sensor_id, uint8_t *value) {
    int sensor_pins[] = {2, 3, 4, 5, 6, 7, 8};  // 传感器引脚
    if (sensor_id < 7) {
        *value = digitalRead(sensor_pins[sensor_id]);
    }
}
```

### 树莓派/Linux平台
```c
// 电机控制 - wiringPi
void Motor_PWM_Callback(uint8_t motor_id, int16_t pwm, bool direction) {
    if (motor_id == 0) {  // 左电机
        digitalWrite(MOTOR_1_PIN, direction ? HIGH : LOW);
        digitalWrite(MOTOR_2_PIN, direction ? LOW : HIGH);
        pwmWrite(PWM_LEFT_PIN, map(pwm, 0, 2000, 0, 1024));
    }
}

// 传感器读取 - wiringPi
void Sensor_Read_Callback(uint8_t sensor_id, uint8_t *value) {
    int sensor_pins[] = {0, 1, 2, 3, 4, 5, 6};  // wiringPi引脚编号
    if (sensor_id < 7) {
        *value = digitalRead(sensor_pins[sensor_id]);
    }
}
```

## 📝 配置适配

### 1. 修改配置参数
```c
// 在robot_control.h中修改这些参数
#define PID_OUTPUT_MAX          1000    // 根据你的PWM范围调整
#define MOTOR_PWM_MAX           1000    // 根据你的电机驱动调整
#define LINE_SENSOR_COUNT       5       // 根据你的传感器数量调整
```

### 2. 调整PID参数
```c
// 根据你的电机特性调整
PID_Init(&pid_left, 20.0f, 2.0f, 0.0f);   // 降低参数值
PID_Init(&pid_right, 20.0f, 2.0f, 0.0f);  // 保持左右一致
```

### 3. 修改控制周期
```c
// 根据你的系统性能调整
#define CONTROL_PERIOD_MS  10   // 可以调整为10ms或更长
```

## 🔍 测试验证

### 1. 基础功能测试
```c
void Test_Basic_Functions(void) {
    // 测试PID控制器
    PID_Controller_t test_pid;
    PID_Init(&test_pid, 1.0f, 0.1f, 0.0f);
    float output = PID_Calculate(&test_pid, 0, 100);
    printf("PID输出: %.2f\n", output);
    
    // 测试数学函数
    int16_t abs_val = Math_Abs(-50);
    printf("绝对值: %d\n", abs_val);
    
    // 测试限幅函数
    int16_t limited = Math_Constrain(150, 0, 100);
    printf("限幅结果: %d\n", limited);
}
```

### 2. 传感器测试
```c
void Test_Sensors(void) {
    LineTracker_Data_t line_data;
    LineTracker_ReadSensors(&line_data);
    
    printf("传感器状态: ");
    for (int i = 0; i < LINE_SENSOR_COUNT; i++) {
        printf("%d ", line_data.sensor_data[i]);
    }
    printf("\n线位置: %d\n", line_data.line_position);
}
```

### 3. 电机测试
```c
void Test_Motors(void) {
    // 测试左电机
    Motor_PWM_Callback(0, 500, true);   // 左电机前进
    delay(1000);
    Motor_PWM_Callback(0, 0, true);     // 停止
    
    // 测试右电机
    Motor_PWM_Callback(1, 500, true);   // 右电机前进
    delay(1000);
    Motor_PWM_Callback(1, 0, true);     // 停止
}
```

## ⚠️ 注意事项

### 1. 硬件兼容性
- 确认电机驱动电压和电流匹配
- 检查传感器逻辑电平兼容性
- 验证PWM频率和分辨率

### 2. 软件兼容性
- 确保编译器支持C99标准
- 检查浮点数运算支持
- 验证定时器精度

### 3. 性能考虑
- 控制周期不要设置过短
- 避免在中断中调用复杂函数
- 合理分配CPU资源

## 🐛 常见移植问题

### 问题1: 编译错误
```
错误: 'bool' undeclared
解决: 添加 #include <stdbool.h>
```

### 问题2: 电机不响应
```
原因: 回调函数未正确实现
解决: 检查Motor_PWM_Callback函数实现
```

### 问题3: 传感器读取异常
```
原因: 传感器引脚配置错误
解决: 检查Sensor_Read_Callback函数中的引脚定义
```

### 问题4: PID控制不稳定
```
原因: PID参数不适合当前系统
解决: 重新调整Kp, Ki, Kd参数
```

## 📚 进阶定制

### 1. 添加新的控制算法
```c
// 在robot_control.c中添加你的算法
float Custom_Control_Algorithm(float input) {
    // 你的算法实现
    return output;
}
```

### 2. 扩展传感器支持
```c
// 修改LINE_SENSOR_COUNT支持更多传感器
#define LINE_SENSOR_COUNT  9  // 支持9路传感器
```

### 3. 添加新的运动模式
```c
typedef enum {
    MOTION_STOP = 0,
    MOTION_FORWARD,
    MOTION_BACKWARD,
    MOTION_TURN_LEFT,
    MOTION_TURN_RIGHT,
    MOTION_CUSTOM  // 你的自定义模式
} Motion_Mode_t;
```

## ✅ 移植检查清单

- [ ] 文件已复制到项目
- [ ] 包含路径已设置
- [ ] 三个回调函数已实现
- [ ] 回调函数已注册
- [ ] 基础功能测试通过
- [ ] 传感器读取正常
- [ ] 电机控制正常
- [ ] PID参数已调优
- [ ] 控制周期已设置

完成以上检查后，你的控制模块就可以正常工作了！

---

**🎉 恭喜！你已经成功移植了智能小车控制模块！**
