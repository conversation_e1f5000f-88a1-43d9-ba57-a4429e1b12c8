/**
 * @file    robot_control.h
 * @brief   智能小车控制模块头文件
 * @note    包含PID控制、电机驱动、循迹算法等核心控制功能
 * <AUTHOR>
 * @date    2024-12-07
 * @version 1.0
 */

#ifndef __ROBOT_CONTROL_H
#define __ROBOT_CONTROL_H

#include <stdint.h>
#include <stdbool.h>

// ==================== 配置参数定义 ====================

// PID控制参数限制
#define PID_OUTPUT_MAX          2000    // PID输出最大值
#define PID_OUTPUT_MIN          -2000   // PID输出最小值

// 电机控制参数
#define MOTOR_PWM_MAX           2000    // 电机PWM最大值
#define MOTOR_PWM_MIN           0       // 电机PWM最小值

// 循迹传感器数量
#define LINE_SENSOR_COUNT       7       // 循迹传感器数量

// 控制周期定义
#define CONTROL_PERIOD_MS       5       // 控制周期(毫秒)

// ==================== 数据结构定义 ====================

/**
 * @brief PID控制器结构体
 * @note  增量式PID控制器，适用于电机速度控制
 */
typedef struct {
    // PID参数
    float Kp;              // 比例系数
    float Ki;              // 积分系数  
    float Kd;              // 微分系数
    
    // 历史误差
    float error_last1;     // 上一次误差 e(k-1)
    float error_last2;     // 上上次误差 e(k-2)
    
    // 输出限制
    float output_max;      // 输出上限
    float output_min;      // 输出下限
    float output_last;     // 上一次输出值
    
    // 控制器状态
    bool enabled;          // 控制器使能标志
} PID_Controller_t;

/**
 * @brief 电机控制结构体
 */
typedef struct {
    int16_t pwm_value;     // PWM值 (带符号)
    bool direction;        // 方向 (true=前进, false=后退)
    bool enabled;          // 电机使能状态
} Motor_Control_t;

/**
 * @brief 循迹传感器数据结构体
 */
typedef struct {
    uint8_t sensor_data[LINE_SENSOR_COUNT];  // 传感器原始数据
    int8_t line_position;                    // 线位置 (-3到+3, 0为中心)
    bool line_detected;                      // 是否检测到线
    uint8_t sensor_count;                    // 触发的传感器数量
} LineTracker_Data_t;

/**
 * @brief 姿态数据结构体
 */
typedef struct {
    float yaw;             // 偏航角 (-180° ~ +180°)
    float yaw_target;      // 目标偏航角
    float yaw_reference;   // 参考偏航角
    bool data_valid;       // 数据有效标志
} Attitude_Data_t;

/**
 * @brief 运动控制指令结构体
 */
typedef struct {
    float target_speed;    // 目标速度
    float target_angle;    // 目标角度
    uint32_t duration_ms;  // 持续时间(毫秒)
    bool completed;        // 完成标志
} Motion_Command_t;

// ==================== 函数声明 ====================

// PID控制相关函数
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd);
void PID_SetLimits(PID_Controller_t *pid, float min, float max);
void PID_Reset(PID_Controller_t *pid);
float PID_Calculate(PID_Controller_t *pid, float current, float target);
void PID_Enable(PID_Controller_t *pid, bool enable);

// 电机控制相关函数
void Motor_Init(void);
void Motor_SetPWM(Motor_Control_t *motor, int16_t pwm);
void Motor_Stop(Motor_Control_t *motor);
void Motor_SetDirection(Motor_Control_t *motor, bool forward);
void Motor_Emergency_Stop(void);

// 循迹控制相关函数
void LineTracker_Init(void);
void LineTracker_ReadSensors(LineTracker_Data_t *data);
int8_t LineTracker_CalculatePosition(const LineTracker_Data_t *data);
void LineTracker_GetMotorSpeeds(const LineTracker_Data_t *data, int16_t *left_speed, int16_t *right_speed);

// 姿态控制相关函数
void Attitude_Init(Attitude_Data_t *attitude);
void Attitude_UpdateYaw(Attitude_Data_t *attitude, float new_yaw);
void Attitude_SetTarget(Attitude_Data_t *attitude, float target_yaw);
void Attitude_SetReference(Attitude_Data_t *attitude, float ref_yaw);
float Attitude_GetError(const Attitude_Data_t *attitude);

// 运动控制相关函数
void Motion_Init(void);
void Motion_StraightLine(Motion_Command_t *cmd, float speed, uint32_t duration);
void Motion_TurnAngle(Motion_Command_t *cmd, float angle, float speed);
void Motion_FollowLine(const LineTracker_Data_t *line_data, int16_t *left_speed, int16_t *right_speed);
void Motion_AttitudeControl(const Attitude_Data_t *attitude, int16_t base_speed, int16_t *left_speed, int16_t *right_speed);
bool Motion_IsCompleted(const Motion_Command_t *cmd);

// 数值处理工具函数
int16_t Math_Abs(int16_t value);
float Math_AbsFloat(float value);
int16_t Math_Constrain(int16_t value, int16_t min, int16_t max);
float Math_ConstrainFloat(float value, float min, float max);
float Math_Map(float value, float in_min, float in_max, float out_min, float out_max);

// 系统控制函数
void RobotControl_Init(void);
void RobotControl_Update(void);
void RobotControl_SetMode(uint8_t mode);
uint8_t RobotControl_GetMode(void);

// 回调函数类型定义 (用户需要实现)
typedef void (*MotorPWM_Callback_t)(uint8_t motor_id, int16_t pwm, bool direction);
typedef void (*SensorRead_Callback_t)(uint8_t sensor_id, uint8_t *value);
typedef void (*AttitudeRead_Callback_t)(float *yaw);

// 回调函数注册
void RobotControl_RegisterMotorCallback(MotorPWM_Callback_t callback);
void RobotControl_RegisterSensorCallback(SensorRead_Callback_t callback);
void RobotControl_RegisterAttitudeCallback(AttitudeRead_Callback_t callback);

#endif /* __ROBOT_CONTROL_H */
