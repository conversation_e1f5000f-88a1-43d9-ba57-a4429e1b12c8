# STM32智能小车系统架构设计文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2024-12-07
- **负责人**: Bob (架构师)
- **项目**: STM32智能小车代码重构

## 2. 系统架构概览

### 2.1 架构设计原则
- **分层解耦**: 采用分层架构，降低模块间耦合度
- **单一职责**: 每个模块只负责特定功能
- **可扩展性**: 便于后续功能扩展和维护
- **性能优先**: 保证实时性要求

### 2.2 整体架构图
```
┌─────────────────────────────────────────┐
│            应用逻辑层 (Application)        │
│  ┌─────────────┐  ┌─────────────────────┐ │
│  │ 状态机管理   │  │   任务调度器         │ │
│  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           控制算法层 (Control)            │
│  ┌─────────────┐  ┌─────────────────────┐ │
│  │ PID控制器   │  │   运动控制算法       │ │
│  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│          设备驱动层 (Drivers)             │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │电机驱动 │ │传感器   │ │ OLED显示    │ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         硬件抽象层 (HAL)                  │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │GPIO控制 │ │定时器   │ │ 串口通信    │ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
```

## 3. 模块详细设计

### 3.1 硬件抽象层 (HAL)

#### 3.1.1 GPIO控制模块
```c
// GPIO抽象接口
typedef enum {
    GPIO_LOW = 0,
    GPIO_HIGH = 1
} GPIO_State_t;

void HAL_GPIO_WritePin_Safe(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin, GPIO_State_t state);
GPIO_State_t HAL_GPIO_ReadPin_Safe(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin);
```

#### 3.1.2 定时器控制模块
```c
// 定时器抽象接口
void HAL_Timer_SetPWM(TIM_HandleTypeDef *htim, uint32_t channel, uint16_t pulse);
uint32_t HAL_Timer_GetEncoder(TIM_HandleTypeDef *htim);
void HAL_Timer_ResetEncoder(TIM_HandleTypeDef *htim);
```

### 3.2 设备驱动层 (Drivers)

#### 3.2.1 电机驱动模块
```c
// 电机控制接口
typedef enum {
    MOTOR_STOP = 0,
    MOTOR_FORWARD,
    MOTOR_BACKWARD
} Motor_Direction_t;

typedef struct {
    Motor_Direction_t direction;
    uint16_t speed;  // 0-2000
} Motor_Control_t;

void Motor_SetControl(Motor_Control_t *left_motor, Motor_Control_t *right_motor);
void Motor_Stop(void);
void Motor_Emergency_Stop(void);
```

#### 3.2.2 传感器驱动模块
```c
// 循迹传感器接口
typedef struct {
    uint8_t sensor_data;  // 7位传感器数据
    int8_t line_position; // 线位置 (-3到+3)
} LineTracker_Data_t;

LineTracker_Data_t LineTracker_Read(void);
bool LineTracker_IsLineDetected(void);

// 姿态传感器接口
typedef struct {
    float yaw;
    float pitch;
    float roll;
} IMU_Data_t;

IMU_Data_t IMU_GetData(void);
void IMU_Reset(void);
```

### 3.3 控制算法层 (Control)

#### 3.3.1 PID控制器重构
```c
// PID控制器结构体优化
typedef struct {
    // PID参数
    float kp, ki, kd;
    
    // 历史误差
    float error_last1, error_last2;
    
    // 输出限制
    float output_min, output_max;
    float output_last;
    
    // 积分限制
    float integral_limit;
    
    // 控制器状态
    bool enabled;
} PID_Controller_t;

// PID控制器接口
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd);
float PID_Calculate(PID_Controller_t *pid, float current, float target);
void PID_Reset(PID_Controller_t *pid);
void PID_SetLimits(PID_Controller_t *pid, float min, float max);
```

#### 3.3.2 运动控制算法
```c
// 运动控制接口
typedef enum {
    MOTION_STOP = 0,
    MOTION_FORWARD,
    MOTION_BACKWARD,
    MOTION_TURN_LEFT,
    MOTION_TURN_RIGHT,
    MOTION_ROTATE_LEFT,
    MOTION_ROTATE_RIGHT
} Motion_Command_t;

typedef struct {
    float target_speed;    // 目标速度
    float target_angle;    // 目标角度
    uint32_t duration_ms;  // 持续时间
} Motion_Params_t;

void Motion_Execute(Motion_Command_t command, Motion_Params_t *params);
void Motion_Stop(void);
bool Motion_IsCompleted(void);
```

### 3.4 应用逻辑层 (Application)

#### 3.4.1 状态机重构设计
```c
// 状态机状态定义
typedef enum {
    STATE_IDLE = 0,
    STATE_LINE_FOLLOWING,
    STATE_TURNING,
    STATE_STRAIGHT_RUNNING,
    STATE_POSITIONING,
    STATE_COMPLETED
} Robot_State_t;

// 状态机事件定义
typedef enum {
    EVENT_START = 0,
    EVENT_LINE_DETECTED,
    EVENT_LINE_LOST,
    EVENT_TURN_COMPLETED,
    EVENT_DISTANCE_REACHED,
    EVENT_ANGLE_REACHED,
    EVENT_STOP
} Robot_Event_t;

// 状态机接口
void StateMachine_Init(void);
void StateMachine_ProcessEvent(Robot_Event_t event);
Robot_State_t StateMachine_GetCurrentState(void);
```

## 4. 重构实施计划

### 4.1 第一阶段：基础重构
1. **提取硬件抽象层**
   - 封装GPIO操作函数
   - 封装定时器操作函数
   - 封装串口操作函数

2. **模块化PID控制器**
   - 重构PID_Increment函数
   - 添加参数验证和错误处理
   - 优化数值计算精度

### 4.2 第二阶段：驱动层重构
1. **电机驱动模块**
   - 封装电机控制逻辑
   - 添加安全保护机制
   - 优化PWM控制精度

2. **传感器驱动模块**
   - 重构循迹传感器读取
   - 优化IMU数据处理
   - 添加数据滤波算法

### 4.3 第三阶段：状态机重构
1. **状态机拆分**
   - 将mode_state()函数拆分为多个子函数
   - 使用状态表驱动状态转换
   - 添加状态转换日志

2. **任务调度优化**
   - 重构定时器中断处理
   - 优化任务执行时序
   - 添加任务优先级管理

## 5. 性能优化策略

### 5.1 内存优化
- 使用栈变量替代全局变量
- 优化数据结构对齐
- 减少不必要的内存拷贝

### 5.2 计算优化
- 使用查表法替代复杂计算
- 优化浮点运算
- 使用位运算优化逻辑判断

### 5.3 实时性优化
- 优化中断处理时间
- 减少关键路径的函数调用
- 使用内联函数优化性能

## 6. 风险评估与缓解

### 6.1 技术风险
- **风险**: 重构可能引入新bug
- **缓解**: 分步骤重构，每步都进行功能验证

### 6.2 性能风险
- **风险**: 抽象层可能影响性能
- **缓解**: 使用内联函数和宏定义优化

### 6.3 兼容性风险
- **风险**: 重构可能影响硬件兼容性
- **缓解**: 保持硬件接口不变，只重构软件逻辑

## 7. 验收标准

### 7.1 功能验收
- 所有原有功能正常工作
- 新增的抽象接口功能正确
- 状态机逻辑清晰可靠

### 7.2 性能验收
- 编译后代码大小增长<5%
- 运行时内存使用优化
- 实时性能不下降

### 7.3 质量验收
- 代码结构清晰，模块化良好
- 函数长度<50行
- 圈复杂度<10
