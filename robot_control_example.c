/**
 * @file    robot_control_example.c
 * @brief   智能小车控制模块使用示例
 * @note    演示如何使用robot_control模块进行小车控制
 * <AUTHOR>
 * @date    2024-12-07
 * @version 1.0
 */

#include "robot_control.h"
#include <stdio.h>

// ==================== 硬件抽象层回调函数实现 ====================

/**
 * @brief  电机PWM输出回调函数
 * @note   用户需要根据实际硬件实现此函数
 * @param  motor_id: 电机ID (0=左电机, 1=右电机)
 * @param  pwm: PWM值 (0-2000)
 * @param  direction: 方向 (true=前进, false=后退)
 * @retval 无
 */
void Motor_PWM_Callback(uint8_t motor_id, int16_t pwm, bool direction)
{
    if (motor_id == 0) {  // 左电机
        // 设置左电机方向
        if (direction) {
            // HAL_GPIO_WritePin(MOTOR_1_GPIO_Port, MOTOR_1_Pin, GPIO_PIN_SET);
            // HAL_GPIO_WritePin(MOTOR_2_GPIO_Port, MOTOR_2_Pin, GPIO_PIN_RESET);
        } else {
            // HAL_GPIO_WritePin(MOTOR_1_GPIO_Port, MOTOR_1_Pin, GPIO_PIN_RESET);
            // HAL_GPIO_WritePin(MOTOR_2_GPIO_Port, MOTOR_2_Pin, GPIO_PIN_SET);
        }
        // 设置左电机PWM
        // __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_1, pwm);
        
        printf("左电机: PWM=%d, 方向=%s\n", pwm, direction ? "前进" : "后退");
    }
    else if (motor_id == 1) {  // 右电机
        // 设置右电机方向
        if (direction) {
            // HAL_GPIO_WritePin(MOTOR_3_GPIO_Port, MOTOR_3_Pin, GPIO_PIN_RESET);
            // HAL_GPIO_WritePin(MOTOR_4_GPIO_Port, MOTOR_4_Pin, GPIO_PIN_SET);
        } else {
            // HAL_GPIO_WritePin(MOTOR_3_GPIO_Port, MOTOR_3_Pin, GPIO_PIN_SET);
            // HAL_GPIO_WritePin(MOTOR_4_GPIO_Port, MOTOR_4_Pin, GPIO_PIN_RESET);
        }
        // 设置右电机PWM
        // __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_2, pwm);
        
        printf("右电机: PWM=%d, 方向=%s\n", pwm, direction ? "前进" : "后退");
    }
}

/**
 * @brief  传感器读取回调函数
 * @note   用户需要根据实际硬件实现此函数
 * @param  sensor_id: 传感器ID (0-6对应G1-G7)
 * @param  value: 传感器值输出指针
 * @retval 无
 */
void Sensor_Read_Callback(uint8_t sensor_id, uint8_t *value)
{
    if (value == NULL) return;
    
    // 根据传感器ID读取对应的GPIO
    switch (sensor_id) {
        case 0:  // G1
            // *value = HAL_GPIO_ReadPin(G1_GPIO_Port, G1_Pin);
            *value = 0;  // 示例值
            break;
        case 1:  // G2
            // *value = HAL_GPIO_ReadPin(G2_GPIO_Port, G2_Pin);
            *value = 0;
            break;
        case 2:  // G3
            // *value = HAL_GPIO_ReadPin(G3_GPIO_Port, G3_Pin);
            *value = 0;
            break;
        case 3:  // G4 (中间传感器)
            // *value = HAL_GPIO_ReadPin(G4_GPIO_Port, G4_Pin);
            *value = 1;  // 示例：检测到线
            break;
        case 4:  // G5
            // *value = HAL_GPIO_ReadPin(G5_GPIO_Port, G5_Pin);
            *value = 0;
            break;
        case 5:  // G6
            // *value = HAL_GPIO_ReadPin(G6_GPIO_Port, G6_Pin);
            *value = 0;
            break;
        case 6:  // G7
            // *value = HAL_GPIO_ReadPin(G7_GPIO_Port, G7_Pin);
            *value = 0;
            break;
        default:
            *value = 0;
            break;
    }
}

/**
 * @brief  姿态读取回调函数
 * @note   用户需要根据实际硬件实现此函数
 * @param  yaw: 偏航角输出指针
 * @retval 无
 */
void Attitude_Read_Callback(float *yaw)
{
    if (yaw == NULL) return;
    
    // 从IMU传感器读取偏航角
    // 这里需要解析UART接收到的IMU数据
    // 示例：假设已经解析好的偏航角
    static float current_yaw = 0.0f;
    *yaw = current_yaw;
    
    printf("当前偏航角: %.2f°\n", *yaw);
}

// ==================== 应用示例函数 ====================

/**
 * @brief  示例1: 基础PID控制
 * @retval 无
 */
void Example_BasicPID(void)
{
    printf("\n=== 示例1: 基础PID控制 ===\n");
    
    // 创建PID控制器
    PID_Controller_t pid_test;
    PID_Init(&pid_test, 1.0f, 0.1f, 0.05f);
    
    // 模拟控制过程
    float target = 100.0f;  // 目标值
    float current = 0.0f;   // 当前值
    
    for (int i = 0; i < 10; i++) {
        // 计算PID输出
        float output = PID_Calculate(&pid_test, current, target);
        
        // 模拟系统响应 (简化模型)
        current += output * 0.01f;
        
        printf("步骤%d: 目标=%.1f, 当前=%.2f, 输出=%.2f\n", i+1, target, current, output);
    }
}

/**
 * @brief  示例2: 循迹控制
 * @retval 无
 */
void Example_LineTracking(void)
{
    printf("\n=== 示例2: 循迹控制 ===\n");
    
    // 创建循迹数据结构
    LineTracker_Data_t line_data;
    
    // 读取传感器数据
    LineTracker_ReadSensors(&line_data);
    
    // 计算电机速度
    int16_t left_speed, right_speed;
    LineTracker_GetMotorSpeeds(&line_data, &left_speed, &right_speed);
    
    printf("传感器状态: ");
    for (int i = 0; i < LINE_SENSOR_COUNT; i++) {
        printf("%d ", line_data.sensor_data[i]);
    }
    printf("\n");
    printf("线位置: %d\n", line_data.line_position);
    printf("检测到线: %s\n", line_data.line_detected ? "是" : "否");
    printf("左轮速度: %d, 右轮速度: %d\n", left_speed, right_speed);
}

/**
 * @brief  示例3: 姿态控制
 * @retval 无
 */
void Example_AttitudeControl(void)
{
    printf("\n=== 示例3: 姿态控制 ===\n");
    
    // 创建姿态数据结构
    Attitude_Data_t attitude;
    Attitude_Init(&attitude);
    
    // 设置目标角度
    Attitude_SetTarget(&attitude, 90.0f);  // 目标转向90度
    
    // 模拟姿态更新
    for (int i = 0; i < 5; i++) {
        float current_yaw = i * 20.0f;  // 模拟当前角度
        Attitude_UpdateYaw(&attitude, current_yaw);
        
        float error = Attitude_GetError(&attitude);
        
        // 计算电机速度
        int16_t left_speed, right_speed;
        Motion_AttitudeControl(&attitude, 20, &left_speed, &right_speed);
        
        printf("当前角度: %.1f°, 目标角度: %.1f°, 误差: %.1f°\n", 
               current_yaw, attitude.yaw_target, error);
        printf("左轮速度: %d, 右轮速度: %d\n", left_speed, right_speed);
    }
}

/**
 * @brief  示例4: 完整控制流程
 * @retval 无
 */
void Example_CompleteControl(void)
{
    printf("\n=== 示例4: 完整控制流程 ===\n");
    
    // 1. 初始化控制系统
    RobotControl_Init();
    
    // 2. 注册回调函数
    RobotControl_RegisterMotorCallback(Motor_PWM_Callback);
    RobotControl_RegisterSensorCallback(Sensor_Read_Callback);
    RobotControl_RegisterAttitudeCallback(Attitude_Read_Callback);
    
    // 3. 创建控制数据结构
    LineTracker_Data_t line_data;
    Attitude_Data_t attitude;
    PID_Controller_t pid_left, pid_right;
    
    // 4. 初始化各模块
    Attitude_Init(&attitude);
    PID_Init(&pid_left, 28.34f, 4.54f, 0.0f);
    PID_Init(&pid_right, 28.34f, 4.54f, -0.66f);
    
    // 5. 模拟控制循环
    printf("开始控制循环...\n");
    for (int cycle = 0; cycle < 3; cycle++) {
        printf("\n--- 控制周期 %d ---\n", cycle + 1);
        
        // 读取传感器
        LineTracker_ReadSensors(&line_data);
        
        // 读取姿态
        float current_yaw;
        Attitude_Read_Callback(&current_yaw);
        Attitude_UpdateYaw(&attitude, current_yaw);
        
        // 计算目标速度
        int16_t target_left, target_right;
        if (line_data.line_detected) {
            // 循迹模式
            LineTracker_GetMotorSpeeds(&line_data, &target_left, &target_right);
            printf("模式: 循迹\n");
        } else {
            // 姿态控制模式
            Motion_AttitudeControl(&attitude, 20, &target_left, &target_right);
            printf("模式: 姿态控制\n");
        }
        
        // PID控制计算
        float left_pwm = PID_Calculate(&pid_left, 0, target_left);   // 假设当前速度为0
        float right_pwm = PID_Calculate(&pid_right, 0, target_right);
        
        // 输出到电机
        Motor_PWM_Callback(0, (int16_t)left_pwm, left_pwm >= 0);
        Motor_PWM_Callback(1, (int16_t)right_pwm, right_pwm >= 0);
        
        printf("目标速度: 左=%d, 右=%d\n", target_left, target_right);
        printf("PID输出: 左=%.1f, 右=%.1f\n", left_pwm, right_pwm);
    }
    
    printf("\n控制循环结束\n");
}

// ==================== 主函数 ====================

/**
 * @brief  主函数 - 运行所有示例
 * @retval 程序退出码
 */
int main(void)
{
    printf("智能小车控制模块使用示例\n");
    printf("========================\n");
    
    // 运行各个示例
    Example_BasicPID();
    Example_LineTracking();
    Example_AttitudeControl();
    Example_CompleteControl();
    
    printf("\n所有示例运行完成！\n");
    return 0;
}
