#include <stdint.h>
#include "Encoder.h"


float PID_Increment(PID_Increment_Struct *PID, float Current, float Target)//����ʽ
{
    float err=0,                                                                                                     //���
          out=0,                                                                                                     //���
          proportion=0,                                                                                              //����
          differential=0;                                                                                            //΢��
    err = (float)Target - (float)Current;                                                                            //�������
    proportion = (float)err - (float)PID->Error_Last1;                                                               //���������
    differential = (float)err - 2 * (float)PID->Error_Last1 + (float)PID->Error_Last2;                               //����΢����
    out = (float)PID->Out_Last + (float)PID->Kp * proportion + (float)PID->Ki * err + (float)PID->Kd * differential; //����PID
    PID->Error_Last2 = PID->Error_Last1;                                                                             //�������ϴ����
    PID->Error_Last1 = err;                                                                                          //�������
    out = Xianfu_float(out,2000);
	PID->Out_Last = out;                                                                                             //�����ϴ����
    return out;
}

/*
float TLY_KP=0.1,TLY_KI=0.1,TLY_KD=0; //λ��PID����ϵ��
int TLY_PID (int position,int target)//position��ǰλ��ֵ targetĿ��λ��ֵ
{ 	
	 static float Bias1=0,Pwm1=0,Last_Bias=0;
	 static float Integral_bias=0;
	 Bias1=target-position;//�������
	 Integral_bias+=Bias1;//����ۼ�
	 Integral_bias=(float)Xianfu(Integral_bias,myabs(target));//����ۼ��޷�
	
	 Pwm1=(TLY_KP*Bias1+TLY_KI*Integral_bias+TLY_KD*(Bias1-Last_Bias))/10;
	 //λ�û�PID��ʽ
	 Last_Bias=Bias1;//Last_BiasΪ��K-1�����                                      
	
	if(Pwm1>100)Pwm1=100;
	if(Pwm1<-100)Pwm1=-100;//����ֵ�޷�
	return (int)Pwm1;                                           
}
*/
int angle(float Angle,float Gyroy,float Mechanical_Angle)
{
	float angle_Kp = 10; //       
  float angle_Kd = 0;//      
	float angle_Bias; //�Ƕ����ֵ
	int angle_balance_up; //ֱ��������PWM
	angle_Bias=Angle-Mechanical_Angle; //�Ƕ����ֵ==�����ĸ�����-����Ƕȣ���еƽ��Ƕȣ�
	angle_balance_up= angle_Kp*angle_Bias+ angle_Kd*Gyroy; //����ƽ����Ƶĵ��PWM  PD����   Up_balance_KP��Pϵ��,Up_balance_KD��Dϵ��
	return angle_balance_up;
}

int myabs(int num)
{
	if(num<0)
		num=-num;
	return num;
}

int Xianfu(int num,int value)
{
	if(num<-value)
		num=-value;
	if(num>value)
		num=value;
	return num;
}

float Xianfu_float(float num,int value)
{
	if(num<-(float)value)
		num=-(float)value;
	if(num>(float)value)
		num=(float)value;
	return num;
}

void set_pwm_left(int num)
{
	if(num<0){left_BACK;}
	else{left_GO;}
	num=myabs(num);
	__HAL_TIM_SetCompare(&htim2,TIM_CHANNEL_1,num);
}

void set_pwm_right(int num)
{
	if(num<0){right_BACK;}
	else{right_GO;}
	num=myabs(num);
	__HAL_TIM_SetCompare(&htim2,TIM_CHANNEL_2,num);
}
