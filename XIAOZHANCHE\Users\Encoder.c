#include <stdint.h>
#include "Encoder.h"


/**
 * @brief  增量式PID控制算法
 * @note   实现增量式PID控制，适用于电机速度控制
 *         增量式PID公式: Δu(k) = Kp*[e(k)-e(k-1)] + Ki*e(k) + Kd*[e(k)-2*e(k-1)+e(k-2)]
 *         输出限幅范围: ±2000
 * @param  PID: PID控制器结构体指针
 * @param  Current: 当前实际值 (编码器反馈值)
 * @param  Target: 目标设定值 (期望速度)
 * @retval 返回PID控制器输出值 (PWM占空比)
 */
float PID_Increment(PID_Increment_Struct *PID, float Current, float Target)  // 增量式PID控制器
{
    float err = 0;          // 当前误差值
    float out = 0;          // PID输出值
    float proportion = 0;   // 比例项增量
    float differential = 0; // 微分项增量

    // ==================== 误差计算 ====================
    err = (float)Target - (float)Current;  // 计算当前误差 = 目标值 - 实际值

    // ==================== PID各项计算 ====================
    proportion = (float)err - (float)PID->Error_Last1;  // 比例项增量 = e(k) - e(k-1)

    // 微分项增量 = e(k) - 2*e(k-1) + e(k-2)
    differential = (float)err - 2 * (float)PID->Error_Last1 + (float)PID->Error_Last2;

    // 增量式PID输出 = 上次输出 + Kp*比例增量 + Ki*当前误差 + Kd*微分增量
    out = (float)PID->Out_Last + (float)PID->Kp * proportion + (float)PID->Ki * err + (float)PID->Kd * differential;

    // ==================== 误差历史更新 ====================
    PID->Error_Last2 = PID->Error_Last1;  // 保存上上次误差 e(k-2) = e(k-1)
    PID->Error_Last1 = err;               // 保存上次误差 e(k-1) = e(k)

    // ==================== 输出限幅处理 ====================
    out = Xianfu_float(out, 2000);  // 限制输出范围在±2000内，防止PWM溢出
    PID->Out_Last = out;             // 保存当前输出，用于下次计算

    return out;  // 返回PID控制器输出值
}

/*
float TLY_KP=0.1,TLY_KI=0.1,TLY_KD=0; //λ��PID����ϵ��
int TLY_PID (int position,int target)//position��ǰλ��ֵ targetĿ��λ��ֵ
{ 	
	 static float Bias1=0,Pwm1=0,Last_Bias=0;
	 static float Integral_bias=0;
	 Bias1=target-position;//�������
	 Integral_bias+=Bias1;//����ۼ�
	 Integral_bias=(float)Xianfu(Integral_bias,myabs(target));//����ۼ��޷�
	
	 Pwm1=(TLY_KP*Bias1+TLY_KI*Integral_bias+TLY_KD*(Bias1-Last_Bias))/10;
	 //λ�û�PID��ʽ
	 Last_Bias=Bias1;//Last_BiasΪ��K-1�����                                      
	
	if(Pwm1>100)Pwm1=100;
	if(Pwm1<-100)Pwm1=-100;//����ֵ�޷�
	return (int)Pwm1;                                           
}
*/
/**
 * @brief  角度平衡控制算法 (PD控制器)
 * @note   用于自平衡小车的角度控制，通过PD算法计算平衡PWM
 *         控制公式: PWM = Kp * 角度误差 + Kd * 角速度
 * @param  Angle: 期望角度 (目标倾斜角度)
 * @param  Gyroy: 当前角速度 (陀螺仪Y轴角速度)
 * @param  Mechanical_Angle: 当前机械角度 (实际倾斜角度)
 * @retval 返回平衡控制PWM值
 */
int angle(float Angle, float Gyroy, float Mechanical_Angle)
{
  float angle_Kp = 10;  // 角度比例系数 (P参数)
  float angle_Kd = 0;   // 角速度微分系数 (D参数)
  float angle_Bias;     // 角度误差值
  int angle_balance_up; // 直立平衡控制PWM输出

  // 计算角度误差 = 期望角度 - 当前机械角度
  angle_Bias = Angle - Mechanical_Angle;

  // PD控制算法: PWM = Kp*角度误差 + Kd*角速度
  angle_balance_up = angle_Kp * angle_Bias + angle_Kd * Gyroy;

  return angle_balance_up;  // 返回平衡控制PWM值
}

/**
 * @brief  整数绝对值函数
 * @note   计算整数的绝对值，用于处理负数
 * @param  num: 输入的整数
 * @retval 返回输入数的绝对值
 */
int myabs(int num)
{
  if(num < 0)    // 如果是负数
    num = -num;  // 转换为正数
  return num;    // 返回绝对值
}

/**
 * @brief  整数限幅函数
 * @note   将输入值限制在指定范围内 [-value, +value]
 * @param  num: 需要限幅的数值
 * @param  value: 限幅的绝对值范围
 * @retval 返回限幅后的数值
 */
int Xianfu(int num, int value)
{
  if(num < -value)   // 如果小于下限
    num = -value;    // 设为下限值
  if(num > value)    // 如果大于上限
    num = value;     // 设为上限值
  return num;        // 返回限幅后的值
}

/**
 * @brief  浮点数限幅函数
 * @note   将浮点数限制在指定范围内 [-value, +value]
 * @param  num: 需要限幅的浮点数
 * @param  value: 限幅的绝对值范围 (整数)
 * @retval 返回限幅后的浮点数
 */
float Xianfu_float(float num, int value)
{
  if(num < -(float)value)   // 如果小于下限
    num = -(float)value;    // 设为下限值
  if(num > (float)value)    // 如果大于上限
    num = (float)value;     // 设为上限值
  return num;               // 返回限幅后的值
}

/**
 * @brief  设置左电机PWM和方向
 * @note   根据输入值的正负控制左电机的转向和速度
 *         正值: 前进方向，负值: 后退方向
 *         PWM通过TIM2的通道1输出
 * @param  num: PWM值 (带符号，范围通常为±2000)
 * @retval 无
 */
void set_pwm_left(int num)
{
  if(num < 0)      // 如果PWM值为负
  {
    left_BACK;     // 设置左电机后退方向 (MOTOR_1=0, MOTOR_2=1)
  }
  else             // 如果PWM值为正或零
  {
    left_GO;       // 设置左电机前进方向 (MOTOR_1=1, MOTOR_2=0)
  }

  num = myabs(num);  // 取绝对值，PWM只能是正值
  __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_1, num);  // 设置TIM2通道1的PWM占空比
}

/**
 * @brief  设置右电机PWM和方向
 * @note   根据输入值的正负控制右电机的转向和速度
 *         正值: 前进方向，负值: 后退方向
 *         PWM通过TIM2的通道2输出
 * @param  num: PWM值 (带符号，范围通常为±2000)
 * @retval 无
 */
void set_pwm_right(int num)
{
  if(num < 0)      // 如果PWM值为负
  {
    right_BACK;    // 设置右电机后退方向 (MOTOR_3=1, MOTOR_4=0)
  }
  else             // 如果PWM值为正或零
  {
    right_GO;      // 设置右电机前进方向 (MOTOR_3=0, MOTOR_4=1)
  }

  num = myabs(num);  // 取绝对值，PWM只能是正值
  __HAL_TIM_SetCompare(&htim2, TIM_CHANNEL_2, num);  // 设置TIM2通道2的PWM占空比
}
