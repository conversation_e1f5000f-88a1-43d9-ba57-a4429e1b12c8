# STM32智能小车用户使用手册

## 1. 快速入门

### 1.1 开箱检查
请确认以下物品齐全：
- STM32F103开发板 × 1
- 智能小车底盘 × 1
- 直流减速电机 × 2
- 编码器 × 2
- 7路红外循迹传感器 × 1
- IMU姿态传感器 × 1
- OLED显示屏 × 1
- 功能按键 × 3
- 激光器模块 × 1
- 连接线若干

### 1.2 硬件连接
#### 1.2.1 电机连接
- 左电机: MOTOR_1 (PB12), MOTOR_2 (PB13)
- 右电机: MOTOR_3 (PB14), MOTOR_4 (PB15)
- 左编码器: TIM3 (PA6, PA7)
- 右编码器: TIM4 (PB6, PB7)

#### 1.2.2 传感器连接
- 循迹传感器: G1(PB5), G2(PB4), G3(PB3), G4(PA8), G5(PA15), G6(PA12), G7(PA11)
- IMU传感器: UART1 (PA9, PA10)
- OLED显示: I2C (PB8, PB9)

#### 1.2.3 控制接口
- 模式按键: Key_mode (PB11)
- 角度按键: Key_yaw (PB10)
- 复位按键: Key_reset (PA3)
- 激光器: L_S (PA4)

### 1.3 软件安装
1. 安装Keil MDK开发环境
2. 安装STM32CubeMX配置工具
3. 安装ST-Link驱动程序
4. 下载项目源代码

## 2. 操作指南

### 2.1 上电启动
1. 连接电源 (推荐7.4V锂电池)
2. 确认所有连接正确
3. 按下电源开关
4. 观察OLED显示屏显示系统信息

### 2.2 模式操作
#### 2.2.1 模式切换
- 按下"模式按键"切换运行模式
- 只有在模式个位数为0时才能切换 (如10→11, 20→21)
- OLED显示当前模式编号

#### 2.2.2 角度设置
- 按下"角度按键"将当前偏航角设为参考角度
- 用于设置小车的初始方向基准
- 显示屏会更新yaw1参数

#### 2.2.3 传感器复位
- 按下"复位按键"重置IMU传感器
- 清除角度累积误差
- 重新校准姿态基准

### 2.3 运行模式说明
#### 2.3.1 基础模式
- **模式10**: 空闲状态，等待启动
- **模式11**: 启动模式，检测循迹线后开始运行
- **模式20**: 精确停止模式

#### 2.3.2 循迹模式
- **模式21-24**: 基础循迹和导航
  - 模式21: 直线循迹
  - 模式22: 循迹行驶
  - 模式23: 转向后继续循迹
  - 模式24: 完成循迹任务

#### 2.3.3 复杂路径模式
- **模式31-48**: 复杂路径规划和执行
  - 包含转向、直行、循迹的组合动作
  - 支持多段路径的连续执行
  - 自动计数和循环控制

### 2.4 显示信息解读
OLED显示屏显示以下信息：
```
yaw: 当前偏航角 (-180° ~ +180°)
yaw0: 目标偏航角
yaw1: 参考偏航角 (按键设置)
mode: 当前运行模式
```

## 3. 调试和维护

### 3.1 参数调整
#### 3.1.1 PID参数调整
如需调整控制性能，可修改以下参数：
```c
// 左轮PID参数
PID_left.Kp = 28.34;  // 比例系数
PID_left.Ki = 4.54;   // 积分系数
PID_left.Kd = 0;      // 微分系数

// 右轮PID参数 (微分系数不同，补偿机械差异)
PID_right.Kp = 28.34;
PID_right.Ki = 4.54;
PID_right.Kd = -0.66;
```

#### 3.1.2 速度参数调整
循迹速度可在`xunji()`函数中调整：
- 直线速度: 30 (可调整为20-40)
- 转弯速度: 1-25 (根据转弯幅度)

### 3.2 传感器校准
#### 3.2.1 循迹传感器校准
1. 将小车放在白色区域，所有传感器应输出0
2. 将小车放在黑线上，对应传感器应输出1
3. 调整传感器高度 (推荐2-5mm)
4. 确保传感器排列整齐

#### 3.2.2 IMU传感器校准
1. 小车静止放置在水平面上
2. 按下复位按键
3. 等待3-5秒完成校准
4. 观察yaw角度是否稳定

### 3.3 故障排除
#### 3.3.1 循迹不稳定
**现象**: 小车在循迹时左右摆动严重
**原因**: 
- PID参数不合适
- 传感器高度不当
- 黑线宽度不匹配

**解决方法**:
1. 降低Kp参数值
2. 调整传感器高度到2-3mm
3. 确保黑线宽度15-20mm

#### 3.3.2 转向不准确
**现象**: 小车转向角度偏差较大
**原因**:
- IMU传感器漂移
- 机械安装不对称
- 电机性能差异

**解决方法**:
1. 重新校准IMU传感器
2. 检查机械安装
3. 调整左右轮PID参数

#### 3.3.3 速度不一致
**现象**: 左右轮速度明显不同
**原因**:
- 电机性能差异
- 编码器连接问题
- PID参数不匹配

**解决方法**:
1. 检查编码器连接
2. 分别调整左右轮PID参数
3. 检查电机和减速器

### 3.4 维护建议
#### 3.4.1 日常维护
- 定期清洁传感器表面
- 检查连接线是否松动
- 保持电池电量充足
- 避免在强光环境下使用

#### 3.4.2 定期检查
- 每周检查螺丝紧固情况
- 每月校准传感器
- 定期更新软件版本
- 备份重要参数设置

## 4. 安全注意事项

### 4.1 使用安全
- 使用前检查所有连接
- 避免短路和过载
- 不要在潮湿环境中使用
- 注意激光器安全，避免直射眼睛

### 4.2 存储安全
- 长期不用时取出电池
- 存放在干燥通风处
- 避免高温和阳光直射
- 定期检查存储状态

## 5. 技术支持

### 5.1 常见问题
如遇到问题，请首先查阅本手册的故障排除部分。

### 5.2 联系方式
- 技术文档: 查看docs目录下的详细技术文档
- 源代码: 参考注释详细的源代码文件
- 在线支持: 通过项目仓库提交问题

### 5.3 更新说明
- 定期检查软件更新
- 关注新功能发布
- 参与社区讨论和改进

## 6. 附录

### 6.1 技术规格
- 工作电压: 6-12V DC
- 工作电流: 1-3A
- 最大速度: 2m/s
- 转向精度: ±1°
- 循迹精度: ±5mm
- 工作温度: 0-50°C

### 6.2 配件清单
- 备用传感器
- 连接线材
- 螺丝螺母
- 电池充电器
- 调试工具

### 6.3 版本历史
- v1.0: 初始版本，基础功能实现
- v1.1: 优化PID参数，提高稳定性
- v1.2: 增加中文注释，改善代码可读性
