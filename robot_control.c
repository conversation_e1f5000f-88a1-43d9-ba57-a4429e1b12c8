/**
 * @file    robot_control.c
 * @brief   智能小车控制模块实现文件
 * @note    实现PID控制、电机驱动、循迹算法等核心控制功能
 * <AUTHOR>
 * @date    2024-12-07
 * @version 1.0
 */

#include "robot_control.h"
#include <string.h>

// ==================== 全局变量定义 ====================

// 控制器实例
static PID_Controller_t pid_left_motor;    // 左电机PID控制器
static PID_Controller_t pid_right_motor;   // 右电机PID控制器

// 电机控制实例
static Motor_Control_t motor_left;         // 左电机控制
static Motor_Control_t motor_right;        // 右电机控制

// 系统状态
static uint8_t current_mode = 0;           // 当前运行模式
static bool system_initialized = false;    // 系统初始化标志

// 回调函数指针
static MotorPWM_Callback_t motor_pwm_callback = NULL;
static SensorRead_Callback_t sensor_read_callback = NULL;
static AttitudeRead_Callback_t attitude_read_callback = NULL;

// ==================== PID控制模块实现 ====================

/**
 * @brief  初始化PID控制器
 * @param  pid: PID控制器指针
 * @param  kp: 比例系数
 * @param  ki: 积分系数
 * @param  kd: 微分系数
 * @retval 无
 */
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd)
{
    if (pid == NULL) return;
    
    // 设置PID参数
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    
    // 初始化历史数据
    pid->error_last1 = 0.0f;
    pid->error_last2 = 0.0f;
    pid->output_last = 0.0f;
    
    // 设置默认限制
    pid->output_max = PID_OUTPUT_MAX;
    pid->output_min = PID_OUTPUT_MIN;
    
    // 使能控制器
    pid->enabled = true;
}

/**
 * @brief  设置PID输出限制
 * @param  pid: PID控制器指针
 * @param  min: 输出下限
 * @param  max: 输出上限
 * @retval 无
 */
void PID_SetLimits(PID_Controller_t *pid, float min, float max)
{
    if (pid == NULL) return;
    
    pid->output_min = min;
    pid->output_max = max;
}

/**
 * @brief  重置PID控制器
 * @param  pid: PID控制器指针
 * @retval 无
 */
void PID_Reset(PID_Controller_t *pid)
{
    if (pid == NULL) return;
    
    pid->error_last1 = 0.0f;
    pid->error_last2 = 0.0f;
    pid->output_last = 0.0f;
}

/**
 * @brief  PID控制器计算 (增量式)
 * @param  pid: PID控制器指针
 * @param  current: 当前实际值
 * @param  target: 目标设定值
 * @retval PID输出值
 */
float PID_Calculate(PID_Controller_t *pid, float current, float target)
{
    if (pid == NULL || !pid->enabled) return 0.0f;
    
    // 计算当前误差
    float error = target - current;
    
    // 计算各项增量
    float proportion = error - pid->error_last1;                                    // 比例项增量
    float differential = error - 2.0f * pid->error_last1 + pid->error_last2;       // 微分项增量
    
    // 增量式PID计算
    float output = pid->output_last + pid->Kp * proportion + pid->Ki * error + pid->Kd * differential;
    
    // 输出限幅
    output = Math_ConstrainFloat(output, pid->output_min, pid->output_max);
    
    // 更新历史数据
    pid->error_last2 = pid->error_last1;
    pid->error_last1 = error;
    pid->output_last = output;
    
    return output;
}

/**
 * @brief  使能/禁用PID控制器
 * @param  pid: PID控制器指针
 * @param  enable: 使能标志
 * @retval 无
 */
void PID_Enable(PID_Controller_t *pid, bool enable)
{
    if (pid == NULL) return;
    
    pid->enabled = enable;
    if (!enable) {
        PID_Reset(pid);  // 禁用时重置控制器
    }
}

// ==================== 电机控制模块实现 ====================

/**
 * @brief  初始化电机控制模块
 * @retval 无
 */
void Motor_Init(void)
{
    // 初始化左电机
    motor_left.pwm_value = 0;
    motor_left.direction = true;  // true=前进
    motor_left.enabled = true;
    
    // 初始化右电机
    motor_right.pwm_value = 0;
    motor_right.direction = true;
    motor_right.enabled = true;
}

/**
 * @brief  设置电机PWM值
 * @param  motor: 电机控制结构体指针
 * @param  pwm: PWM值 (带符号)
 * @retval 无
 */
void Motor_SetPWM(Motor_Control_t *motor, int16_t pwm)
{
    if (motor == NULL || !motor->enabled) return;
    
    // 确定方向
    motor->direction = (pwm >= 0);
    
    // 取绝对值并限幅
    motor->pwm_value = Math_Constrain(Math_Abs(pwm), 0, MOTOR_PWM_MAX);
}

/**
 * @brief  停止电机
 * @param  motor: 电机控制结构体指针
 * @retval 无
 */
void Motor_Stop(Motor_Control_t *motor)
{
    if (motor == NULL) return;
    
    motor->pwm_value = 0;
}

/**
 * @brief  设置电机方向
 * @param  motor: 电机控制结构体指针
 * @param  forward: 前进标志 (true=前进, false=后退)
 * @retval 无
 */
void Motor_SetDirection(Motor_Control_t *motor, bool forward)
{
    if (motor == NULL) return;
    
    motor->direction = forward;
}

/**
 * @brief  紧急停止所有电机
 * @retval 无
 */
void Motor_Emergency_Stop(void)
{
    Motor_Stop(&motor_left);
    Motor_Stop(&motor_right);
    
    // 调用硬件层停止函数
    if (motor_pwm_callback != NULL) {
        motor_pwm_callback(0, 0, true);  // 左电机停止
        motor_pwm_callback(1, 0, true);  // 右电机停止
    }
}

// ==================== 循迹控制模块实现 ====================

/**
 * @brief  初始化循迹模块
 * @retval 无
 */
void LineTracker_Init(void)
{
    // 循迹模块初始化 (如果需要特殊初始化)
}

/**
 * @brief  读取循迹传感器数据
 * @param  data: 循迹数据结构体指针
 * @retval 无
 */
void LineTracker_ReadSensors(LineTracker_Data_t *data)
{
    if (data == NULL || sensor_read_callback == NULL) return;
    
    // 读取所有传感器数据
    for (int i = 0; i < LINE_SENSOR_COUNT; i++) {
        sensor_read_callback(i, &data->sensor_data[i]);
    }
    
    // 计算线位置
    data->line_position = LineTracker_CalculatePosition(data);
    
    // 检查是否检测到线
    data->line_detected = false;
    data->sensor_count = 0;
    for (int i = 0; i < LINE_SENSOR_COUNT; i++) {
        if (data->sensor_data[i] == 1) {
            data->line_detected = true;
            data->sensor_count++;
        }
    }
}

/**
 * @brief  计算线的位置
 * @param  data: 循迹数据结构体指针
 * @retval 线位置 (-3到+3, 0为中心)
 */
int8_t LineTracker_CalculatePosition(const LineTracker_Data_t *data)
{
    if (data == NULL) return 0;
    
    // 加权平均法计算线位置
    int32_t weighted_sum = 0;
    int32_t total_weight = 0;
    
    for (int i = 0; i < LINE_SENSOR_COUNT; i++) {
        if (data->sensor_data[i] == 1) {
            weighted_sum += (i - 3) * data->sensor_data[i];  // 中心为0，左侧为负，右侧为正
            total_weight += data->sensor_data[i];
        }
    }
    
    if (total_weight == 0) return 0;  // 没有检测到线
    
    return (int8_t)(weighted_sum / total_weight);
}

/**
 * @brief  根据循迹数据计算电机速度
 * @param  data: 循迹数据结构体指针
 * @param  left_speed: 左电机速度输出
 * @param  right_speed: 右电机速度输出
 * @retval 无
 */
void LineTracker_GetMotorSpeeds(const LineTracker_Data_t *data, int16_t *left_speed, int16_t *right_speed)
{
    if (data == NULL || left_speed == NULL || right_speed == NULL) return;
    
    const int16_t base_speed = 30;  // 基础速度
    const int16_t max_speed = 30;   // 最大速度
    const int16_t min_speed = 1;    // 最小速度
    
    if (!data->line_detected) {
        // 没有检测到线，停止
        *left_speed = 0;
        *right_speed = 0;
        return;
    }
    
    // 根据线位置调整速度
    switch (data->line_position) {
        case -3:  // 线在最左侧，大幅右转
            *left_speed = max_speed;
            *right_speed = min_speed;
            break;
        case -2:  // 线在左侧，中等右转
            *left_speed = max_speed;
            *right_speed = 5;
            break;
        case -1:  // 线在左中，小幅右转
            *left_speed = max_speed;
            *right_speed = 15;
            break;
        case 0:   // 线在中间，直行
            *left_speed = base_speed;
            *right_speed = base_speed;
            break;
        case 1:   // 线在右中，小幅左转
            *left_speed = 15;
            *right_speed = max_speed;
            break;
        case 2:   // 线在右侧，中等左转
            *left_speed = 5;
            *right_speed = max_speed;
            break;
        case 3:   // 线在最右侧，大幅左转
            *left_speed = min_speed;
            *right_speed = max_speed;
            break;
        default:
            *left_speed = 0;
            *right_speed = 0;
            break;
    }
}

// ==================== 姿态控制模块实现 ====================

/**
 * @brief  初始化姿态控制模块
 * @param  attitude: 姿态数据结构体指针
 * @retval 无
 */
void Attitude_Init(Attitude_Data_t *attitude)
{
    if (attitude == NULL) return;

    attitude->yaw = 0.0f;
    attitude->yaw_target = 0.0f;
    attitude->yaw_reference = 0.0f;
    attitude->data_valid = false;
}

/**
 * @brief  更新偏航角数据
 * @param  attitude: 姿态数据结构体指针
 * @param  new_yaw: 新的偏航角值
 * @retval 无
 */
void Attitude_UpdateYaw(Attitude_Data_t *attitude, float new_yaw)
{
    if (attitude == NULL) return;

    attitude->yaw = new_yaw;
    attitude->data_valid = true;
}

/**
 * @brief  设置目标偏航角
 * @param  attitude: 姿态数据结构体指针
 * @param  target_yaw: 目标偏航角
 * @retval 无
 */
void Attitude_SetTarget(Attitude_Data_t *attitude, float target_yaw)
{
    if (attitude == NULL) return;

    attitude->yaw_target = target_yaw;
}

/**
 * @brief  设置参考偏航角
 * @param  attitude: 姿态数据结构体指针
 * @param  ref_yaw: 参考偏航角
 * @retval 无
 */
void Attitude_SetReference(Attitude_Data_t *attitude, float ref_yaw)
{
    if (attitude == NULL) return;

    attitude->yaw_reference = ref_yaw;
}

/**
 * @brief  获取偏航角误差
 * @param  attitude: 姿态数据结构体指针
 * @retval 偏航角误差
 */
float Attitude_GetError(const Attitude_Data_t *attitude)
{
    if (attitude == NULL || !attitude->data_valid) return 0.0f;

    return attitude->yaw_target - attitude->yaw;
}

// ==================== 运动控制模块实现 ====================

/**
 * @brief  初始化运动控制模块
 * @retval 无
 */
void Motion_Init(void)
{
    // 运动控制模块初始化
}

/**
 * @brief  直线运动控制
 * @param  cmd: 运动指令结构体指针
 * @param  speed: 运动速度
 * @param  duration: 持续时间(毫秒)
 * @retval 无
 */
void Motion_StraightLine(Motion_Command_t *cmd, float speed, uint32_t duration)
{
    if (cmd == NULL) return;

    cmd->target_speed = speed;
    cmd->target_angle = 0.0f;  // 直线运动角度为0
    cmd->duration_ms = duration;
    cmd->completed = false;
}

/**
 * @brief  转向运动控制
 * @param  cmd: 运动指令结构体指针
 * @param  angle: 转向角度
 * @param  speed: 转向速度
 * @retval 无
 */
void Motion_TurnAngle(Motion_Command_t *cmd, float angle, float speed)
{
    if (cmd == NULL) return;

    cmd->target_speed = speed;
    cmd->target_angle = angle;
    cmd->duration_ms = 0;  // 角度控制不使用时间限制
    cmd->completed = false;
}

/**
 * @brief  循迹运动控制
 * @param  line_data: 循迹数据
 * @param  left_speed: 左电机速度输出
 * @param  right_speed: 右电机速度输出
 * @retval 无
 */
void Motion_FollowLine(const LineTracker_Data_t *line_data, int16_t *left_speed, int16_t *right_speed)
{
    LineTracker_GetMotorSpeeds(line_data, left_speed, right_speed);
}

/**
 * @brief  姿态控制运动
 * @param  attitude: 姿态数据
 * @param  base_speed: 基础速度
 * @param  left_speed: 左电机速度输出
 * @param  right_speed: 右电机速度输出
 * @retval 无
 */
void Motion_AttitudeControl(const Attitude_Data_t *attitude, int16_t base_speed, int16_t *left_speed, int16_t *right_speed)
{
    if (attitude == NULL || left_speed == NULL || right_speed == NULL) return;

    float angle_error = Attitude_GetError(attitude);

    // 根据角度误差调整左右轮速度
    if (angle_error < -1.0f) {  // 需要左转
        *left_speed = base_speed - 2;
        *right_speed = base_speed + 2;
    }
    else if (angle_error > 1.0f) {  // 需要右转
        *left_speed = base_speed + 2;
        *right_speed = base_speed - 2;
    }
    else {  // 角度正确，直行
        *left_speed = base_speed;
        *right_speed = base_speed;
    }
}

/**
 * @brief  检查运动是否完成
 * @param  cmd: 运动指令结构体指针
 * @retval 完成状态
 */
bool Motion_IsCompleted(const Motion_Command_t *cmd)
{
    if (cmd == NULL) return true;

    return cmd->completed;
}

// ==================== 数值处理工具函数实现 ====================

/**
 * @brief  计算整数绝对值
 * @param  value: 输入值
 * @retval 绝对值
 */
int16_t Math_Abs(int16_t value)
{
    return (value < 0) ? -value : value;
}

/**
 * @brief  计算浮点数绝对值
 * @param  value: 输入值
 * @retval 绝对值
 */
float Math_AbsFloat(float value)
{
    return (value < 0.0f) ? -value : value;
}

/**
 * @brief  整数限幅函数
 * @param  value: 输入值
 * @param  min: 最小值
 * @param  max: 最大值
 * @retval 限幅后的值
 */
int16_t Math_Constrain(int16_t value, int16_t min, int16_t max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

/**
 * @brief  浮点数限幅函数
 * @param  value: 输入值
 * @param  min: 最小值
 * @param  max: 最大值
 * @retval 限幅后的值
 */
float Math_ConstrainFloat(float value, float min, float max)
{
    if (value < min) return min;
    if (value > max) return max;
    return value;
}

/**
 * @brief  数值映射函数
 * @param  value: 输入值
 * @param  in_min: 输入最小值
 * @param  in_max: 输入最大值
 * @param  out_min: 输出最小值
 * @param  out_max: 输出最大值
 * @retval 映射后的值
 */
float Math_Map(float value, float in_min, float in_max, float out_min, float out_max)
{
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

// ==================== 系统控制函数实现 ====================

/**
 * @brief  初始化机器人控制系统
 * @retval 无
 */
void RobotControl_Init(void)
{
    // 初始化PID控制器 (使用原项目的优化参数)
    PID_Init(&pid_left_motor, 28.34f, 4.54f, 0.0f);
    PID_Init(&pid_right_motor, 28.34f, 4.54f, -0.66f);  // 右轮微分系数不同，补偿机械差异

    // 初始化电机控制
    Motor_Init();

    // 初始化循迹模块
    LineTracker_Init();

    // 初始化运动控制
    Motion_Init();

    // 设置系统初始化标志
    system_initialized = true;
    current_mode = 0;
}

/**
 * @brief  更新机器人控制系统 (需要周期性调用)
 * @retval 无
 */
void RobotControl_Update(void)
{
    if (!system_initialized) return;

    // 这里可以添加周期性的控制逻辑
    // 例如：PID控制更新、状态检查等
}

/**
 * @brief  设置运行模式
 * @param  mode: 运行模式
 * @retval 无
 */
void RobotControl_SetMode(uint8_t mode)
{
    current_mode = mode;
}

/**
 * @brief  获取当前运行模式
 * @retval 当前模式
 */
uint8_t RobotControl_GetMode(void)
{
    return current_mode;
}

// ==================== 回调函数注册实现 ====================

/**
 * @brief  注册电机PWM回调函数
 * @param  callback: 回调函数指针
 * @retval 无
 */
void RobotControl_RegisterMotorCallback(MotorPWM_Callback_t callback)
{
    motor_pwm_callback = callback;
}

/**
 * @brief  注册传感器读取回调函数
 * @param  callback: 回调函数指针
 * @retval 无
 */
void RobotControl_RegisterSensorCallback(SensorRead_Callback_t callback)
{
    sensor_read_callback = callback;
}

/**
 * @brief  注册姿态读取回调函数
 * @param  callback: 回调函数指针
 * @retval 无
 */
void RobotControl_RegisterAttitudeCallback(AttitudeRead_Callback_t callback)
{
    attitude_read_callback = callback;
}
